# UAV Dynamics Simulation System

A comprehensive Python-based simulation framework for quadrotor UAV dynamics, control, and visualization.

## Features

- **Complete 6-DOF Dynamics**: Full six degree-of-freedom quadrotor dynamics with Newton-Euler equations
- **Modular Architecture**: Component-based design similar to Simulink block diagrams
- **Advanced Control**: Support for PID, cascaded control, and custom control algorithms
- **Real-time Visualization**: 3D trajectory viewer and real-time state monitoring
- **Data Recording**: Comprehensive data logging with multiple output formats
- **Performance Analysis**: Built-in tools for trajectory analysis and performance metrics

## System Architecture

```
uav_python/
├── core/                   # Core simulation framework
│   ├── simulation.py       # Main simulation engine
│   ├── component.py        # Base component classes
│   ├── state.py           # UAV state representation
│   └── utils.py           # Utility functions
├── dynamics/              # UAV dynamics models
│   ├── quadrotor.py       # 6-DOF quadrotor dynamics
│   └── propulsion.py      # Motor and propulsion system
├── control/               # Control system components
│   ├── base_controller.py # Controller base classes
│   └── pid_controller.py  # PID controller implementations
├── visualization/         # Visualization tools
│   ├── real_time_plot.py  # Real-time plotting
│   └── trajectory_viewer.py # 3D trajectory visualization
├── data/                  # Data recording and analysis
│   ├── recorder.py        # Data recording system
│   └── analyzer.py        # Performance analysis tools
└── examples/              # Example simulations
    ├── basic_hover_simulation.py
    └── trajectory_following.py
```

## Quick Start

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd uav_python
   ```

2. Install dependencies:
   ```bash
   pip install numpy scipy matplotlib h5py pandas
   ```

### Basic Usage

Run a simple hover simulation:

```bash
python examples/basic_hover_simulation.py
```

Run trajectory following simulation:

```bash
python examples/trajectory_following.py
```

## Core Components

### 1. UAV Dynamics Model

The system implements a complete 6-DOF quadrotor dynamics model:

```python
from dynamics.quadrotor import QuadrotorDynamics
from core.state import UAVState

# Create dynamics model
dynamics = QuadrotorDynamics(
    mass=1.5,                           # kg
    inertia=np.diag([0.029, 0.029, 0.055]),  # kg*m^2
    gravity=9.81,                       # m/s^2
    drag_coefficient=0.01
)

# Initial state
initial_state = UAVState(
    position=np.array([0, 0, -1]),      # m
    velocity=np.zeros(3),               # m/s
    attitude=np.zeros(3),               # rad (roll, pitch, yaw)
    angular_velocity=np.zeros(3)        # rad/s
)
```

### 2. Control System

Supports multiple control architectures:

```python
from control.pid_controller import AttitudePIDController
from control.base_controller import AttitudeReference

# Create attitude controller
controller = AttitudePIDController(mass=1.5, gravity=9.81)

# Set PID gains
controller.set_attitude_gains('roll', kp=6.0, ki=0.2, kd=0.3)
controller.set_attitude_gains('pitch', kp=6.0, ki=0.2, kd=0.3)
controller.set_attitude_gains('yaw', kp=4.0, ki=0.1, kd=0.2)

# Create reference
reference = AttitudeReference(roll=0, pitch=0, yaw=0)

# Compute control
control_output = controller.compute_control(reference, current_state, dt)
```

### 3. Simulation Framework

Modular simulation engine:

```python
from core.simulation import Simulation
from core.component import DynamicsComponent, ControllerComponent

# Create simulation
sim = Simulation(dt=0.01, real_time=False)

# Add components
dynamics_comp = DynamicsComponent("uav_dynamics", dynamics_model)
controller_comp = ControllerComponent("controller", controller)

sim.add_component(dynamics_comp, update_priority=1)
sim.add_component(controller_comp, update_priority=0)

# Connect components
sim.connect_components("controller", "control_output", 
                      "uav_dynamics", "control_inputs")

# Run simulation
sim.run(duration=10.0)
```

### 4. Visualization

Real-time monitoring and 3D visualization:

```python
from visualization.real_time_plot import UAVStateMonitor
from visualization.trajectory_viewer import TrajectoryViewer3D

# State monitoring
monitor = UAVStateMonitor()
monitor.start()

# 3D trajectory viewer
viewer = TrajectoryViewer3D()
viewer.start_animation()

# Update during simulation
monitor.update_state(time, state)
viewer.add_state_data(time, position, attitude)
```

### 5. Data Recording

Comprehensive data logging:

```python
from data.recorder import DataRecorder
from data.analyzer import DataAnalyzer

# Set up recording
recorder = DataRecorder()
recorder.start_recording("test_session")

# Record simulation step
recorder.record_step(time, simulation_results)

# Save data
recorder.save_data("simulation_data", format='hdf5')

# Analysis
analyzer = DataAnalyzer()
analyzer.load_data(data)
metrics = analyzer.compute_trajectory_metrics()
report = analyzer.generate_performance_report()
```

## Physical Model

### Dynamics Equations

The system implements the complete Newton-Euler equations:

**Translational Dynamics:**
```
m * dv/dt = F_gravity + F_thrust + F_drag
```

**Rotational Dynamics:**
```
I * dω/dt + ω × (I * ω) = M_control
```

### Quadrotor Configuration

X-configuration with motor layout:
```
    1
  ╱   ╲
 ╱     ╲
4   +   2
 ╲     ╱
  ╲   ╱
    3
```

Motor mixing matrix maps control inputs [thrust, roll_moment, pitch_moment, yaw_moment] to individual motor speeds.

## Control Architecture

### Attitude Control

Inner-loop attitude stabilization using PID controllers:
- Roll/Pitch: Controls attitude angles to achieve desired orientation
- Yaw: Controls heading angle
- Thrust: Controls vertical acceleration

### Position Control (Optional)

Outer-loop position control:
- Generates attitude references from position errors
- Cascaded with inner-loop attitude control
- Supports waypoint following and trajectory tracking

## Examples

### Hover Simulation

Demonstrates basic attitude control:
- UAV starts with small disturbances
- Controller stabilizes to hover position
- Real-time monitoring of all states
- Performance analysis and reporting

### Trajectory Following

Advanced demonstration:
- Complex trajectory generation (circle, figure-8)
- Cascaded position and attitude control
- 3D visualization with reference trajectory
- Comprehensive tracking error analysis

## Performance Metrics

The system computes various performance metrics:

- **Trajectory Metrics**: Length, velocity statistics, smoothness
- **Control Performance**: Settling time, overshoot, steady-state error  
- **Stability Metrics**: Oscillation frequency, damping ratio
- **Tracking Performance**: RMS error, maximum error, cross-track error

## Customization

### Adding New Controllers

```python
from control.base_controller import BaseController

class MyController(BaseController):
    def __init__(self, name):
        super().__init__(name)
    
    def compute_control(self, reference, current_state, dt):
        # Implement your control algorithm
        return control_output
    
    def reset(self):
        # Reset internal states
        pass
```

### Custom Dynamics

```python
from dynamics.quadrotor import QuadrotorDynamics

class MyDynamics(QuadrotorDynamics):
    def compute_derivatives(self, state, control_inputs):
        # Add your custom dynamics
        derivatives = super().compute_derivatives(state, control_inputs)
        # Modify derivatives as needed
        return derivatives
```

### Custom Trajectories

```python
from control.base_controller import TrajectoryReference

def generate_spiral_trajectory(radius, height_rate, duration):
    trajectory = TrajectoryReference()
    
    for t in np.arange(0, duration, 0.1):
        x = radius * np.cos(t)
        y = radius * np.sin(t)
        z = -height_rate * t
        
        trajectory.add_waypoint(t, np.array([x, y, z]))
    
    return trajectory
```

## Advanced Features

### Multi-Rate Simulation

Different components can run at different update rates:

```python
sim.add_component(dynamics_comp, update_priority=0)      # High priority
sim.add_component(controller_comp, update_priority=1)    # Medium priority  
sim.add_component(logger_comp, update_priority=2)       # Low priority
```

### Real-Time Operation

Enable real-time simulation for hardware-in-the-loop testing:

```python
sim = Simulation(dt=0.01, real_time=True)
```

### Batch Simulation

Run multiple simulations with parameter variations:

```python
for mass in [1.0, 1.5, 2.0]:
    dynamics = QuadrotorDynamics(mass=mass)
    # Run simulation and collect results
```

## Troubleshooting

### Common Issues

1. **Numerical Instability**: Reduce time step or check controller gains
2. **Slow Performance**: Disable real-time mode or reduce visualization updates
3. **Import Errors**: Ensure you're in the correct directory

### Performance Tips

- Use `real_time=False` for faster-than-real-time simulation
- Reduce visualization update rates for better performance
- Use HDF5 format for large datasets

## Contributing

1. Fork the repository
2. Create a feature branch
3. Implement your changes with tests
4. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## References

- Beard, R. W., & McLain, T. W. (2012). Small unmanned aircraft: Theory and practice
- Stevens, B. L., Lewis, F. L., & Johnson, E. N. (2016). Aircraft control and simulation
- Mahony, R., Kumar, V., & Corke, P. (2012). Multirotor aerial vehicles