"""
Base component class for modular simulation architecture
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
import numpy as np


class Component(ABC):
    """
    Base class for all simulation components
    
    Provides standardized interface for connecting and updating components
    in a block diagram fashion similar to Simulink.
    """
    
    def __init__(self, name: str):
        """
        Initialize component
        
        Args:
            name: Component name/identifier
        """
        self.name = name
        self.inputs = {}
        self.outputs = {}
        self.parameters = {}
        self.enabled = True
        
        # Connection management
        self.input_connections = {}  # Input port -> (source_component, output_port)
        self.output_connections = {}  # Output port -> List[(target_component, input_port)]
    
    @abstractmethod
    def initialize(self, **kwargs):
        """Initialize component with parameters"""
        pass
    
    @abstractmethod
    def update(self, dt: float) -> Dict[str, Any]:
        """
        Update component for one time step
        
        Args:
            dt: Time step (s)
            
        Returns:
            Dictionary of output values
        """
        pass
    
    @abstractmethod
    def reset(self):
        """Reset component to initial state"""
        pass
    
    def set_input(self, port_name: str, value: Any):
        """Set input port value"""
        self.inputs[port_name] = value
    
    def get_input(self, port_name: str, default: Any = None) -> Any:
        """Get input port value"""
        return self.inputs.get(port_name, default)
    
    def set_output(self, port_name: str, value: Any):
        """Set output port value"""
        self.outputs[port_name] = value
    
    def get_output(self, port_name: str, default: Any = None) -> Any:
        """Get output port value"""
        return self.outputs.get(port_name, default)
    
    def set_parameter(self, name: str, value: Any):
        """Set component parameter"""
        self.parameters[name] = value
    
    def get_parameter(self, name: str, default: Any = None) -> Any:
        """Get component parameter"""
        return self.parameters.get(name, default)
    
    def connect_input(self, input_port: str, source_component: 'Component', 
                     source_port: str):
        """
        Connect input port to another component's output
        
        Args:
            input_port: This component's input port name
            source_component: Source component
            source_port: Source component's output port name
        """
        self.input_connections[input_port] = (source_component, source_port)
        
        # Add to source component's output connections
        if source_port not in source_component.output_connections:
            source_component.output_connections[source_port] = []
        source_component.output_connections[source_port].append((self, input_port))
    
    def update_inputs(self):
        """Update input values from connected components"""
        for input_port, (source_comp, source_port) in self.input_connections.items():
            if source_comp.enabled:
                value = source_comp.get_output(source_port)
                if value is not None:
                    self.set_input(input_port, value)
    
    def get_info(self) -> Dict[str, Any]:
        """Get component information"""
        return {
            'name': self.name,
            'type': self.__class__.__name__,
            'enabled': self.enabled,
            'inputs': list(self.inputs.keys()),
            'outputs': list(self.outputs.keys()),
            'parameters': self.parameters.copy()
        }


class DynamicsComponent(Component):
    """Component wrapper for dynamics models"""
    
    def __init__(self, name: str, dynamics_model):
        super().__init__(name)
        self.dynamics_model = dynamics_model
        self.state = None
    
    def initialize(self, initial_state=None, **kwargs):
        """Initialize with dynamics model and initial state"""
        if initial_state is not None:
            self.state = initial_state
        
        # Set up input/output ports
        self.inputs = {
            'control_inputs': np.zeros(4),  # Motor commands
            'disturbances': np.zeros(3)     # External disturbances
        }
        
        self.outputs = {
            'state': self.state,
            'position': None,
            'velocity': None,
            'attitude': None,
            'angular_velocity': None
        }
    
    def update(self, dt: float) -> Dict[str, Any]:
        """Update dynamics for one time step"""
        if not self.enabled or self.state is None:
            return self.outputs
        
        # Get control inputs
        control_inputs = self.get_input('control_inputs', np.zeros(4))
        
        # Compute state derivatives
        derivatives = self.dynamics_model.compute_derivatives(self.state, control_inputs)
        
        # Integrate using Euler method (can be upgraded to RK4)
        state_vector = self.state.state_vector + dt * derivatives
        self.state.state_vector = state_vector
        self.state.time += dt
        
        # Update outputs
        self.outputs.update({
            'state': self.state,
            'position': self.state.position.copy(),
            'velocity': self.state.velocity.copy(),
            'attitude': self.state.attitude.copy(),
            'angular_velocity': self.state.angular_velocity.copy()
        })
        
        return self.outputs
    
    def reset(self):
        """Reset to initial state"""
        if self.state is not None:
            self.state.state_vector = np.zeros(12)
            self.state.time = 0.0


class ControllerComponent(Component):
    """Component wrapper for controllers"""
    
    def __init__(self, name: str, controller):
        super().__init__(name)
        self.controller = controller
    
    def initialize(self, **kwargs):
        """Initialize controller component"""
        # Set up input/output ports
        self.inputs = {
            'reference': None,
            'current_state': None,
            'feedback': None
        }
        
        self.outputs = {
            'control_output': np.zeros(4),  # Motor commands or control signals
            'error': None
        }
    
    def update(self, dt: float) -> Dict[str, Any]:
        """Update controller for one time step"""
        if not self.enabled:
            return self.outputs
        
        # Get inputs
        reference = self.get_input('reference')
        current_state = self.get_input('current_state')
        
        if reference is not None and current_state is not None:
            # Compute control output
            control_output = self.controller.compute_control(
                reference, current_state, dt
            )
            
            # Update outputs
            self.outputs['control_output'] = control_output
            if hasattr(self.controller, 'error'):
                self.outputs['error'] = self.controller.error
        
        return self.outputs
    
    def reset(self):
        """Reset controller"""
        if hasattr(self.controller, 'reset'):
            self.controller.reset()


class SensorComponent(Component):
    """Component wrapper for sensor models"""
    
    def __init__(self, name: str, sensor_model):
        super().__init__(name)
        self.sensor_model = sensor_model
    
    def initialize(self, **kwargs):
        """Initialize sensor component"""
        self.inputs = {
            'true_state': None,
            'noise_level': 0.0
        }
        
        self.outputs = {
            'measurement': None,
            'noise': None
        }
    
    def update(self, dt: float) -> Dict[str, Any]:
        """Update sensor for one time step"""
        if not self.enabled:
            return self.outputs
        
        true_state = self.get_input('true_state')
        noise_level = self.get_input('noise_level', 0.0)
        
        if true_state is not None:
            measurement = self.sensor_model.measure(true_state, noise_level)
            self.outputs['measurement'] = measurement
        
        return self.outputs
    
    def reset(self):
        """Reset sensor"""
        if hasattr(self.sensor_model, 'reset'):
            self.sensor_model.reset()