"""
Main simulation engine
"""

import numpy as np
import time
from typing import List, Dict, Any, Optional, Callable
from .component import Component
from .state import StateHistory


class Simulation:
    """
    Main simulation engine that manages components and time stepping
    
    Provides functionality similar to Simulink's simulation engine,
    managing component updates, data flow, and timing.
    """
    
    def __init__(self, dt: float = 0.01, real_time: bool = False):
        """
        Initialize simulation
        
        Args:
            dt: Simulation time step (s)
            real_time: Whether to run in real-time mode
        """
        self.dt = dt
        self.real_time = real_time
        self.current_time = 0.0
        self.step_count = 0
        
        # Component management
        self.components = {}  # name -> Component
        self.update_order = []  # Ordered list of component names
        
        # Data recording
        self.data_recorder = None
        self.state_history = StateHistory()
        
        # Simulation control
        self.is_running = False
        self.should_stop = False
        
        # Callbacks
        self.step_callbacks = []  # Functions called after each step
        self.start_callbacks = []  # Functions called at simulation start
        self.stop_callbacks = []  # Functions called at simulation stop
        
        # Performance monitoring
        self.execution_times = []
        self.real_time_factor = 1.0
    
    def add_component(self, component: Component, update_priority: int = 0):
        """
        Add component to simulation
        
        Args:
            component: Component to add
            update_priority: Update priority (lower values updated first)
        """
        if component.name in self.components:
            raise ValueError(f"Component '{component.name}' already exists")
        
        self.components[component.name] = component
        
        # Insert in update order based on priority
        inserted = False
        for i, (name, priority) in enumerate(self.update_order):
            if update_priority < priority:
                self.update_order.insert(i, (component.name, update_priority))
                inserted = True
                break
        
        if not inserted:
            self.update_order.append((component.name, update_priority))
    
    def remove_component(self, name: str):
        """Remove component from simulation"""
        if name in self.components:
            del self.components[name]
            self.update_order = [(n, p) for n, p in self.update_order if n != name]
    
    def get_component(self, name: str) -> Optional[Component]:
        """Get component by name"""
        return self.components.get(name)
    
    def connect_components(self, source_name: str, source_port: str,
                          target_name: str, target_port: str):
        """
        Connect two components
        
        Args:
            source_name: Source component name
            source_port: Source output port
            target_name: Target component name  
            target_port: Target input port
        """
        source_comp = self.components.get(source_name)
        target_comp = self.components.get(target_name)
        
        if not source_comp:
            raise ValueError(f"Source component '{source_name}' not found")
        if not target_comp:
            raise ValueError(f"Target component '{target_name}' not found")
        
        target_comp.connect_input(target_port, source_comp, source_port)
    
    def initialize(self, **kwargs):
        """Initialize all components"""
        for component in self.components.values():
            component.initialize(**kwargs)
    
    def reset(self):
        """Reset simulation and all components"""
        self.current_time = 0.0
        self.step_count = 0
        self.is_running = False
        self.should_stop = False
        
        # Reset all components
        for component in self.components.values():
            component.reset()
        
        # Clear data
        self.state_history.clear()
        if self.data_recorder:
            self.data_recorder.clear()
        
        self.execution_times.clear()
    
    def step(self) -> Dict[str, Any]:
        """
        Execute one simulation step
        
        Returns:
            Dictionary with simulation results
        """
        step_start_time = time.time()
        
        # Update all components in order
        step_results = {}
        
        for component_name, _ in self.update_order:
            component = self.components[component_name]
            
            if component.enabled:
                # Update inputs from connected components
                component.update_inputs()
                
                # Update component
                output = component.update(self.dt)
                step_results[component_name] = output
        
        # Update simulation time
        self.current_time += self.dt
        self.step_count += 1
        
        # Record execution time
        execution_time = time.time() - step_start_time
        self.execution_times.append(execution_time)
        
        # Calculate real-time factor
        if execution_time > 0:
            self.real_time_factor = self.dt / execution_time
        
        # Call step callbacks
        for callback in self.step_callbacks:
            callback(self, step_results)
        
        # Real-time synchronization
        if self.real_time and execution_time < self.dt:
            time.sleep(self.dt - execution_time)
        
        return step_results
    
    def run(self, duration: Optional[float] = None, 
            max_steps: Optional[int] = None,
            stop_condition: Optional[Callable] = None):
        """
        Run simulation
        
        Args:
            duration: Simulation duration (s)
            max_steps: Maximum number of steps
            stop_condition: Function that returns True to stop simulation
        """
        if self.is_running:
            print("Simulation is already running")
            return
        
        # Call start callbacks
        for callback in self.start_callbacks:
            callback(self)
        
        self.is_running = True
        self.should_stop = False
        
        try:
            while self.is_running and not self.should_stop:
                # Check stop conditions
                if duration and self.current_time >= duration:
                    break
                if max_steps and self.step_count >= max_steps:
                    break
                if stop_condition and stop_condition(self):
                    break
                
                # Execute one step
                self.step()
                
        except KeyboardInterrupt:
            print("\nSimulation interrupted by user")
        
        finally:
            self.is_running = False
            
            # Call stop callbacks
            for callback in self.stop_callbacks:
                callback(self)
    
    def stop(self):
        """Stop simulation"""
        self.should_stop = True
    
    def add_callback(self, callback_type: str, callback: Callable):
        """
        Add callback function
        
        Args:
            callback_type: 'step', 'start', or 'stop'
            callback: Callback function
        """
        if callback_type == 'step':
            self.step_callbacks.append(callback)
        elif callback_type == 'start':
            self.start_callbacks.append(callback)
        elif callback_type == 'stop':
            self.stop_callbacks.append(callback)
        else:
            raise ValueError(f"Unknown callback type: {callback_type}")
    
    def set_data_recorder(self, recorder):
        """Set data recorder"""
        self.data_recorder = recorder
        
        # Add recording callback
        def record_step(sim, results):
            recorder.record_step(sim.current_time, results)
        
        self.add_callback('step', record_step)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get simulation statistics"""
        if not self.execution_times:
            return {}
        
        exec_times = np.array(self.execution_times)
        
        return {
            'total_time': self.current_time,
            'total_steps': self.step_count,
            'dt': self.dt,
            'avg_execution_time': np.mean(exec_times),
            'max_execution_time': np.max(exec_times),
            'min_execution_time': np.min(exec_times),
            'avg_real_time_factor': np.mean([self.dt / t for t in exec_times if t > 0]),
            'current_real_time_factor': self.real_time_factor
        }
    
    def get_component_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all components"""
        return {name: comp.get_info() for name, comp in self.components.items()}
    
    def print_status(self):
        """Print current simulation status"""
        stats = self.get_statistics()
        
        print(f"\n--- Simulation Status ---")
        print(f"Time: {self.current_time:.3f} s")
        print(f"Steps: {self.step_count}")
        print(f"Components: {len(self.components)}")
        print(f"Real-time factor: {self.real_time_factor:.2f}x")
        
        if stats:
            print(f"Avg execution time: {stats['avg_execution_time']*1000:.2f} ms")
            print(f"Max execution time: {stats['max_execution_time']*1000:.2f} ms")
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        if self.is_running:
            self.stop()