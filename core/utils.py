"""
Utility functions for UAV simulation
"""

import numpy as np
from typing import <PERSON>ple, Union


def euler_to_rotation_matrix(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    Convert Euler angles to rotation matrix (Z-Y-X convention)
    
    Args:
        roll: Roll angle (phi) in radians
        pitch: Pitch angle (theta) in radians  
        yaw: Yaw angle (psi) in radians
        
    Returns:
        3x3 rotation matrix from body to world frame
    """
    cos_phi, sin_phi = np.cos(roll), np.sin(roll)
    cos_theta, sin_theta = np.cos(pitch), np.sin(pitch)
    cos_psi, sin_psi = np.cos(yaw), np.sin(yaw)
    
    R = np.array([
        [cos_theta * cos_psi, 
         sin_phi * sin_theta * cos_psi - cos_phi * sin_psi,
         cos_phi * sin_theta * cos_psi + sin_phi * sin_psi],
        
        [cos_theta * sin_psi,
         sin_phi * sin_theta * sin_psi + cos_phi * cos_psi,
         cos_phi * sin_theta * sin_psi - sin_phi * cos_psi],
        
        [-sin_theta,
         sin_phi * cos_theta,
         cos_phi * cos_theta]
    ])
    
    return R


def rotation_matrix_to_euler(R: np.ndarray) -> Tuple[float, float, float]:
    """
    Convert rotation matrix to Euler angles (Z-Y-X convention)
    
    Args:
        R: 3x3 rotation matrix
        
    Returns:
        Tuple of (roll, pitch, yaw) in radians
    """
    # Extract angles, handling singularities
    sin_theta = -R[2, 0]
    sin_theta = np.clip(sin_theta, -1.0, 1.0)
    theta = np.arcsin(sin_theta)
    
    if np.abs(np.cos(theta)) > 1e-6:
        # Non-singular case
        phi = np.arctan2(R[2, 1], R[2, 2])
        psi = np.arctan2(R[1, 0], R[0, 0])
    else:
        # Gimbal lock case
        phi = 0.0
        if sin_theta > 0:  # theta = pi/2
            psi = np.arctan2(-R[0, 1], R[1, 1])
        else:  # theta = -pi/2
            psi = np.arctan2(R[0, 1], R[1, 1])
    
    return phi, theta, psi


def quaternion_to_euler(q: np.ndarray) -> Tuple[float, float, float]:
    """
    Convert quaternion to Euler angles
    
    Args:
        q: Quaternion [w, x, y, z]
        
    Returns:
        Tuple of (roll, pitch, yaw) in radians
    """
    w, x, y, z = q
    
    # Roll
    sin_r_cp = 2 * (w * x + y * z)
    cos_r_cp = 1 - 2 * (x * x + y * y)
    roll = np.arctan2(sin_r_cp, cos_r_cp)
    
    # Pitch
    sin_p = 2 * (w * y - z * x)
    sin_p = np.clip(sin_p, -1.0, 1.0)
    pitch = np.arcsin(sin_p)
    
    # Yaw
    sin_y_cp = 2 * (w * z + x * y)
    cos_y_cp = 1 - 2 * (y * y + z * z)
    yaw = np.arctan2(sin_y_cp, cos_y_cp)
    
    return roll, pitch, yaw


def euler_to_quaternion(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    Convert Euler angles to quaternion
    
    Args:
        roll: Roll angle in radians
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians
        
    Returns:
        Quaternion [w, x, y, z]
    """
    cy = np.cos(yaw * 0.5)
    sy = np.sin(yaw * 0.5)
    cp = np.cos(pitch * 0.5)
    sp = np.sin(pitch * 0.5)
    cr = np.cos(roll * 0.5)
    sr = np.sin(roll * 0.5)
    
    w = cy * cp * cr + sy * sp * sr
    x = cy * cp * sr - sy * sp * cr
    y = sy * cp * sr + cy * sp * cr
    z = sy * cp * cr - cy * sp * sr
    
    return np.array([w, x, y, z])


def wrap_angle(angle: float) -> float:
    """
    Wrap angle to [-pi, pi]
    
    Args:
        angle: Angle in radians
        
    Returns:
        Wrapped angle in [-pi, pi]
    """
    return np.arctan2(np.sin(angle), np.cos(angle))


def angle_difference(angle1: float, angle2: float) -> float:
    """
    Compute shortest angular difference between two angles
    
    Args:
        angle1: First angle in radians
        angle2: Second angle in radians
        
    Returns:
        Angular difference in [-pi, pi]
    """
    diff = angle1 - angle2
    return wrap_angle(diff)


def skew_symmetric(vector: np.ndarray) -> np.ndarray:
    """
    Create skew-symmetric matrix from 3D vector
    
    Args:
        vector: 3D vector [x, y, z]
        
    Returns:
        3x3 skew-symmetric matrix
    """
    x, y, z = vector
    return np.array([
        [0, -z, y],
        [z, 0, -x],
        [-y, x, 0]
    ])


def saturate(value: Union[float, np.ndarray], 
             min_val: Union[float, np.ndarray], 
             max_val: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """
    Saturate value between min and max limits
    
    Args:
        value: Value to saturate
        min_val: Minimum limit
        max_val: Maximum limit
        
    Returns:
        Saturated value
    """
    return np.clip(value, min_val, max_val)


def low_pass_filter(current_value: float, new_value: float, 
                   alpha: float) -> float:
    """
    Apply low-pass filter
    
    Args:
        current_value: Current filtered value
        new_value: New measurement
        alpha: Filter coefficient (0 < alpha < 1)
        
    Returns:
        Filtered value
    """
    return alpha * new_value + (1 - alpha) * current_value


def rk4_integrate(f, y, t, dt, *args):
    """
    Fourth-order Runge-Kutta integration
    
    Args:
        f: Function to integrate (dy/dt = f(t, y, *args))
        y: Current state
        t: Current time
        dt: Time step
        *args: Additional arguments for f
        
    Returns:
        Next state value
    """
    k1 = dt * f(t, y, *args)
    k2 = dt * f(t + dt/2, y + k1/2, *args)
    k3 = dt * f(t + dt/2, y + k2/2, *args)
    k4 = dt * f(t + dt, y + k3, *args)
    
    return y + (k1 + 2*k2 + 2*k3 + k4) / 6


def normalize_vector(vector: np.ndarray) -> np.ndarray:
    """
    Normalize a vector to unit length
    
    Args:
        vector: Input vector
        
    Returns:
        Normalized vector
    """
    norm = np.linalg.norm(vector)
    if norm > 1e-12:
        return vector / norm
    else:
        return vector


def compute_control_allocation_matrix(arm_length: float, 
                                    thrust_coeff: float,
                                    torque_coeff: float) -> np.ndarray:
    """
    Compute control allocation matrix for X-configuration quadrotor
    
    Args:
        arm_length: Distance from center to motor (m)
        thrust_coeff: Thrust coefficient
        torque_coeff: Torque coefficient
        
    Returns:
        4x4 allocation matrix mapping [T, Mx, My, Mz] to motor speeds squared
    """
    L = arm_length / np.sqrt(2)  # Effective arm length for X-config
    
    # Allocation matrix: motor_speeds^2 = A * [T, Mx, My, Mz]
    A = np.array([
        # Motor 1 (front-right)
        [1/(4*thrust_coeff), -1/(2*L*thrust_coeff), -1/(2*L*thrust_coeff), -1/(4*torque_coeff)],
        # Motor 2 (front-left)
        [1/(4*thrust_coeff), 1/(2*L*thrust_coeff), -1/(2*L*thrust_coeff), 1/(4*torque_coeff)],
        # Motor 3 (back-left)
        [1/(4*thrust_coeff), 1/(2*L*thrust_coeff), 1/(2*L*thrust_coeff), -1/(4*torque_coeff)],
        # Motor 4 (back-right)
        [1/(4*thrust_coeff), -1/(2*L*thrust_coeff), 1/(2*L*thrust_coeff), 1/(4*torque_coeff)]
    ])
    
    return A


def compute_inertia_matrix(mass: float, dimensions: Tuple[float, float, float]) -> np.ndarray:
    """
    Compute inertia matrix for rectangular UAV body
    
    Args:
        mass: Vehicle mass (kg)
        dimensions: (length, width, height) in meters
        
    Returns:
        3x3 inertia matrix
    """
    l, w, h = dimensions
    
    Ixx = mass * (w**2 + h**2) / 12
    Iyy = mass * (l**2 + h**2) / 12
    Izz = mass * (l**2 + w**2) / 12
    
    return np.diag([Ixx, Iyy, Izz])


def wind_disturbance_model(time: float, wind_speed: float = 2.0,
                          wind_direction: float = 0.0,
                          turbulence_intensity: float = 0.1) -> np.ndarray:
    """
    Generate wind disturbance forces
    
    Args:
        time: Current time (s)
        wind_speed: Mean wind speed (m/s)
        wind_direction: Wind direction (rad)
        turbulence_intensity: Turbulence intensity (0-1)
        
    Returns:
        Wind force vector [Fx, Fy, Fz] in world frame
    """
    # Mean wind
    wind_x = wind_speed * np.cos(wind_direction)
    wind_y = wind_speed * np.sin(wind_direction)
    wind_z = 0.0
    
    # Turbulence (simplified as sinusoidal)
    turb_freq = 2 * np.pi * 0.5  # 0.5 Hz
    turbulence_x = turbulence_intensity * wind_speed * np.sin(turb_freq * time)
    turbulence_y = turbulence_intensity * wind_speed * np.cos(turb_freq * time * 1.3)
    turbulence_z = turbulence_intensity * wind_speed * 0.5 * np.sin(turb_freq * time * 0.7)
    
    return np.array([
        wind_x + turbulence_x,
        wind_y + turbulence_y,
        wind_z + turbulence_z
    ])


def compute_drag_force(velocity: np.ndarray, drag_coefficient: float) -> np.ndarray:
    """
    Compute aerodynamic drag force
    
    Args:
        velocity: Velocity vector (m/s)
        drag_coefficient: Drag coefficient
        
    Returns:
        Drag force vector (N)
    """
    speed = np.linalg.norm(velocity)
    if speed > 1e-6:
        drag_direction = -velocity / speed
        drag_magnitude = drag_coefficient * speed**2
        return drag_magnitude * drag_direction
    else:
        return np.zeros(3)