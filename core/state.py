"""
State representation for UAV simulation
"""

import numpy as np
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class UAVState:
    """
    Six degree-of-freedom UAV state representation
    
    Position: [x, y, z] in world frame (m)
    Velocity: [vx, vy, vz] in world frame (m/s)
    Attitude: [phi, theta, psi] Euler angles (rad)
    Angular velocity: [p, q, r] in body frame (rad/s)
    """
    
    # Position in world frame [x, y, z] (m)
    position: np.ndarray = None
    
    # Linear velocity in world frame [vx, vy, vz] (m/s)
    velocity: np.ndarray = None
    
    # Attitude as Euler angles [phi, theta, psi] (rad)
    attitude: np.ndarray = None
    
    # Angular velocity in body frame [p, q, r] (rad/s)
    angular_velocity: np.ndarray = None
    
    # Time stamp
    time: float = 0.0
    
    def __post_init__(self):
        """Initialize arrays if not provided"""
        if self.position is None:
            self.position = np.zeros(3)
        if self.velocity is None:
            self.velocity = np.zeros(3)
        if self.attitude is None:
            self.attitude = np.zeros(3)
        if self.angular_velocity is None:
            self.angular_velocity = np.zeros(3)
    
    @property
    def state_vector(self) -> np.ndarray:
        """Get complete state as 12D vector"""
        return np.concatenate([
            self.position,
            self.velocity,
            self.attitude,
            self.angular_velocity
        ])
    
    @state_vector.setter
    def state_vector(self, state: np.ndarray):
        """Set state from 12D vector"""
        if len(state) != 12:
            raise ValueError("State vector must be 12-dimensional")
        
        self.position = state[0:3]
        self.velocity = state[3:6]
        self.attitude = state[6:9]
        self.angular_velocity = state[9:12]
    
    def copy(self) -> 'UAVState':
        """Create a copy of the current state"""
        return UAVState(
            position=self.position.copy(),
            velocity=self.velocity.copy(),
            attitude=self.attitude.copy(),
            angular_velocity=self.angular_velocity.copy(),
            time=self.time
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for logging"""
        return {
            'time': self.time,
            'position': self.position.tolist(),
            'velocity': self.velocity.tolist(),
            'attitude': self.attitude.tolist(),
            'angular_velocity': self.angular_velocity.tolist()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UAVState':
        """Create state from dictionary"""
        return cls(
            position=np.array(data['position']),
            velocity=np.array(data['velocity']),
            attitude=np.array(data['attitude']),
            angular_velocity=np.array(data['angular_velocity']),
            time=data['time']
        )


class StateHistory:
    """Container for storing state history"""
    
    def __init__(self, max_size: Optional[int] = None):
        self.states = []
        self.max_size = max_size
    
    def add_state(self, state: UAVState):
        """Add a state to history"""
        self.states.append(state.copy())
        
        if self.max_size and len(self.states) > self.max_size:
            self.states.pop(0)
    
    def get_trajectory(self) -> Dict[str, np.ndarray]:
        """Get trajectory data as arrays"""
        if not self.states:
            return {}
        
        times = np.array([s.time for s in self.states])
        positions = np.array([s.position for s in self.states])
        velocities = np.array([s.velocity for s in self.states])
        attitudes = np.array([s.attitude for s in self.states])
        angular_velocities = np.array([s.angular_velocity for s in self.states])
        
        return {
            'time': times,
            'position': positions,
            'velocity': velocities,
            'attitude': attitudes,
            'angular_velocity': angular_velocities
        }
    
    def clear(self):
        """Clear all stored states"""
        self.states.clear()
    
    def __len__(self):
        return len(self.states)
    
    def __getitem__(self, index):
        return self.states[index]