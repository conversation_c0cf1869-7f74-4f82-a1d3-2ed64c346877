"""
Propulsion system model with motor dynamics
"""

import numpy as np
from typing import Tu<PERSON>, List, Dict, Any


class Motor:
    """
    Individual motor model with first-order dynamics
    """
    
    def __init__(self, time_constant: float = 0.02, 
                 max_speed: float = 1000.0, min_speed: float = 0.0):
        """
        Initialize motor parameters
        
        Args:
            time_constant: Motor time constant (s)
            max_speed: Maximum motor speed (rad/s)
            min_speed: Minimum motor speed (rad/s)
        """
        self.time_constant = time_constant
        self.max_speed = max_speed
        self.min_speed = min_speed
        
        # Current motor speed
        self.current_speed = 0.0
        
        # Motor command
        self.commanded_speed = 0.0
    
    def update(self, dt: float, command: float) -> float:
        """
        Update motor speed with first-order dynamics
        
        Args:
            dt: Time step (s)
            command: Commanded motor speed (rad/s)
            
        Returns:
            Current motor speed (rad/s)
        """
        # Saturate command
        self.commanded_speed = np.clip(command, self.min_speed, self.max_speed)
        
        # First-order dynamics: τ * dω/dt + ω = ω_cmd
        alpha = dt / (self.time_constant + dt)
        self.current_speed = (1 - alpha) * self.current_speed + alpha * self.commanded_speed
        
        return self.current_speed
    
    def reset(self):
        """Reset motor to zero speed"""
        self.current_speed = 0.0
        self.commanded_speed = 0.0


class PropulsionSystem:
    """
    Complete propulsion system with four motors
    """
    
    def __init__(self, num_motors: int = 4, motor_params: Dict[str, float] = None):
        """
        Initialize propulsion system
        
        Args:
            num_motors: Number of motors
            motor_params: Motor parameters dictionary
        """
        self.num_motors = num_motors
        
        # Default motor parameters
        default_params = {
            'time_constant': 0.02,
            'max_speed': 1000.0,
            'min_speed': 0.0
        }
        
        if motor_params:
            default_params.update(motor_params)
        
        # Create motors
        self.motors = [Motor(**default_params) for _ in range(num_motors)]
        
        # Propulsion model parameters
        self.thrust_coefficient = 8.54858e-06  # N/(rad/s)^2
        self.torque_coefficient = 0.016  # N*m/(rad/s)^2
        self.arm_length = 0.25  # m
        
        # Motor mixing matrix for control allocation
        self.mixing_matrix = self._create_mixing_matrix()
    
    def _create_mixing_matrix(self) -> np.ndarray:
        """
        Create control mixing matrix for X-configuration quadrotor
        
        Maps [thrust_cmd, roll_cmd, pitch_cmd, yaw_cmd] to motor speeds squared
        
        Returns:
            4x4 mixing matrix
        """
        # X-configuration mixing matrix
        # Motor layout: 1-front, 2-right, 3-back, 4-left
        L = self.arm_length / np.sqrt(2)  # Effective arm length
        kt = self.thrust_coefficient
        kq = self.torque_coefficient
        
        # Mixing matrix: motor_speeds^2 = M * [T, Mx, My, Mz]
        M = np.array([
            # Motor 1 (Front-right)
            [1/(4*kt), -1/(2*L*kt), -1/(2*L*kt), -1/(4*kq)],
            # Motor 2 (Front-left)  
            [1/(4*kt), 1/(2*L*kt), -1/(2*L*kt), 1/(4*kq)],
            # Motor 3 (Back-left)
            [1/(4*kt), 1/(2*L*kt), 1/(2*L*kt), -1/(4*kq)],
            # Motor 4 (Back-right)
            [1/(4*kt), -1/(2*L*kt), 1/(2*L*kt), 1/(4*kq)]
        ])
        
        return M
    
    def allocate_control(self, control_vector: np.ndarray) -> np.ndarray:
        """
        Allocate control commands to individual motors
        
        Args:
            control_vector: [thrust, roll_moment, pitch_moment, yaw_moment]
            
        Returns:
            Motor speed commands [w1, w2, w3, w4] (rad/s)
        """
        thrust, Mx, My, Mz = control_vector
        
        # Direct control allocation for X-configuration
        L = self.arm_length / np.sqrt(2)  # Effective arm length
        kt = self.thrust_coefficient
        kq = self.torque_coefficient
        
        # Individual motor thrusts from control allocation
        # Solve the mixing equations directly
        f_base = thrust / 4.0  # Base thrust per motor
        
        # Moment contributions
        f_roll = Mx / (2 * L)     # Roll moment contribution
        f_pitch = My / (2 * L)    # Pitch moment contribution  
        f_yaw = Mz / 4.0         # Yaw moment contribution (simplified)
        
        # Motor thrusts (ensuring non-negative)
        f1 = max(0.01, f_base - f_roll - f_pitch - f_yaw)  # Front-right
        f2 = max(0.01, f_base + f_roll - f_pitch + f_yaw)  # Front-left
        f3 = max(0.01, f_base + f_roll + f_pitch - f_yaw)  # Back-left
        f4 = max(0.01, f_base - f_roll + f_pitch + f_yaw)  # Back-right
        
        # Convert thrusts to motor speeds
        motor_speeds = np.array([
            np.sqrt(f1 / kt),
            np.sqrt(f2 / kt),
            np.sqrt(f3 / kt),
            np.sqrt(f4 / kt)
        ])
        
        return motor_speeds
    
    def update_motors(self, dt: float, motor_commands: np.ndarray) -> np.ndarray:
        """
        Update all motors with dynamics (numpy optimized)
        
        Args:
            dt: Time step (s)
            motor_commands: Commanded motor speeds [w1, w2, w3, w4] (rad/s)
            
        Returns:
            Actual motor speeds (rad/s)
        """
        # Vectorized motor dynamics update
        # Extract current states
        current_speeds = np.array([motor.current_speed for motor in self.motors])
        
        # Apply saturation limits (vectorized)
        motor_commands_sat = np.clip(motor_commands, 
                                   [motor.min_speed for motor in self.motors],
                                   [motor.max_speed for motor in self.motors])
        
        # First-order dynamics update (vectorized)
        time_constants = np.array([motor.time_constant for motor in self.motors])
        alpha = dt / (time_constants + dt)
        
        # Update all motors simultaneously
        new_speeds = (1 - alpha) * current_speeds + alpha * motor_commands_sat
        
        # Update motor states
        for i, motor in enumerate(self.motors):
            motor.current_speed = new_speeds[i]
            motor.commanded_speed = motor_commands_sat[i]
        
        return new_speeds
    
    def compute_forces_moments(self, motor_speeds: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        Compute total thrust and moments from motor speeds
        
        Args:
            motor_speeds: [w1, w2, w3, w4] (rad/s)
            
        Returns:
            Total thrust (N) and moments [Mx, My, Mz] (N*m)
        """
        w1, w2, w3, w4 = motor_speeds
        
        # Individual motor thrusts
        thrusts = self.thrust_coefficient * motor_speeds**2
        
        # Total thrust
        total_thrust = np.sum(thrusts)
        
        # Individual motor torques (reactive torques)
        torques = self.torque_coefficient * motor_speeds**2
        
        # Moments in body frame (X-configuration)
        L = self.arm_length / np.sqrt(2)  # Effective arm length
        
        # Roll moment (about x-axis)
        Mx = L * (thrusts[1] - thrusts[3])
        
        # Pitch moment (about y-axis)
        My = L * (thrusts[2] - thrusts[0])
        
        # Yaw moment (about z-axis) - from reactive torques
        Mz = torques[0] - torques[1] + torques[2] - torques[3]
        
        moments = np.array([Mx, My, Mz])
        
        return total_thrust, moments
    
    def get_motor_states(self) -> Dict[str, List[float]]:
        """Get current motor states"""
        return {
            'current_speeds': [motor.current_speed for motor in self.motors],
            'commanded_speeds': [motor.commanded_speed for motor in self.motors]
        }
    
    def reset_motors(self):
        """Reset all motors to zero speed"""
        for motor in self.motors:
            motor.reset()
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get propulsion system parameters"""
        return {
            'num_motors': self.num_motors,
            'thrust_coefficient': self.thrust_coefficient,
            'torque_coefficient': self.torque_coefficient,
            'arm_length': self.arm_length,
            'motor_time_constant': self.motors[0].time_constant,
            'motor_max_speed': self.motors[0].max_speed,
            'motor_min_speed': self.motors[0].min_speed
        }


class ThrustCurve:
    """
    Advanced thrust curve model for more realistic motor behavior
    """
    
    def __init__(self, coefficients: List[float] = None):
        """
        Initialize thrust curve
        
        Args:
            coefficients: Polynomial coefficients [a0, a1, a2, ...] 
                         for thrust = a0 + a1*ω + a2*ω² + ...
        """
        # Default quadratic model: T = k*ω²
        if coefficients is None:
            self.coefficients = [0, 0, 8.54858e-06]
        else:
            self.coefficients = coefficients
    
    def compute_thrust(self, motor_speed: float) -> float:
        """
        Compute thrust from motor speed using polynomial model
        
        Args:
            motor_speed: Motor speed (rad/s)
            
        Returns:
            Thrust force (N)
        """
        thrust = 0.0
        for i, coeff in enumerate(self.coefficients):
            thrust += coeff * (motor_speed ** i)
        
        return max(0, thrust)  # Ensure non-negative thrust
    
    def compute_thrust_derivative(self, motor_speed: float) -> float:
        """
        Compute thrust derivative dT/dω
        
        Args:
            motor_speed: Motor speed (rad/s)
            
        Returns:
            Thrust derivative (N*s/rad)
        """
        derivative = 0.0
        for i, coeff in enumerate(self.coefficients[1:], 1):
            derivative += i * coeff * (motor_speed ** (i-1))
        
        return derivative