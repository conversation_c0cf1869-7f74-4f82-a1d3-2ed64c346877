"""
Quadrotor dynamics model implementation
"""

import numpy as np
from typing import <PERSON><PERSON>, Dict, Any
from core.state import UAVState


class QuadrotorDynamics:
    """
    Six degree-of-freedom quadrotor dynamics model
    
    Implements Newton-Euler equations for rigid body motion
    including gravity, thrust, and aerodynamic effects.
    """
    
    def __init__(self, mass: float = 1.5, inertia: np.ndarray = None,
                 gravity: float = 9.81, drag_coefficient: float = 0.01):
        """
        Initialize quadrotor parameters
        
        Args:
            mass: Vehicle mass (kg)
            inertia: Inertia matrix (3x3) (kg*m^2)
            gravity: Gravitational acceleration (m/s^2)
            drag_coefficient: Air drag coefficient
        """
        self.mass = mass
        self.gravity = gravity
        self.drag_coefficient = drag_coefficient
        
        # Default inertia matrix for typical quadrotor
        if inertia is None:
            self.inertia = np.diag([0.029, 0.029, 0.055])
        else:
            self.inertia = inertia
        
        self.inertia_inv = np.linalg.inv(self.inertia)
        
        # Quadrotor physical parameters
        self.arm_length = 0.25  # Distance from center to motor (m)
        self.thrust_coefficient = 8.54858e-06  # Thrust coefficient
        self.torque_coefficient = 0.016  # Torque coefficient
        
    def compute_derivatives(self, state: UAVState, 
                          control_inputs: np.ndarray) -> np.ndarray:
        """
        Compute state derivatives for numerical integration (numpy optimized)
        
        Args:
            state: Current UAV state
            control_inputs: Motor speeds [w1, w2, w3, w4] (rad/s)
            
        Returns:
            State derivatives [12x1]
        """
        # Extract state variables (direct references for speed)
        vel = state.velocity
        att = state.attitude  # [phi, theta, psi]
        omega = state.angular_velocity  # [p, q, r]
        
        phi, theta, psi = att
        
        # Clamp attitudes to prevent gimbal lock and numerical issues
        phi = np.clip(phi, -np.pi/2 + 0.1, np.pi/2 - 0.1)
        theta = np.clip(theta, -np.pi/2 + 0.1, np.pi/2 - 0.1)
        psi = np.fmod(psi, 2*np.pi)  # Keep yaw within [-2π, 2π]
        
        # Pre-compute trigonometric values (vectorized)
        cos_phi, sin_phi = np.cos(phi), np.sin(phi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        cos_psi, sin_psi = np.cos(psi), np.sin(psi)
        
        # Optimized rotation matrix computation (inline)
        R_11 = cos_theta * cos_psi
        R_12 = sin_phi * sin_theta * cos_psi - cos_phi * sin_psi
        R_13 = cos_phi * sin_theta * cos_psi + sin_phi * sin_psi
        R_31 = -sin_theta
        R_32 = sin_phi * cos_theta
        R_33 = cos_phi * cos_theta
        
        # Forces and moments from propulsion system
        thrust, moments = self._compute_thrust_and_moments(control_inputs)
        
        # Force computation (vectorized)
        # Thrust force in world frame (body thrust is upward: [0,0,thrust])
        thrust_world_x = R_13 * thrust
        thrust_world_y = (cos_phi * sin_theta * sin_psi - sin_phi * cos_psi) * thrust
        thrust_world_z = R_33 * thrust
        
        # Gravitational force (downward)
        gravity_world_z = self.mass * self.gravity
        
        # Air drag (vectorized)
        drag_coeff_vel = self.drag_coefficient * vel
        
        # Total acceleration (vectorized computation)
        inv_mass = 1.0 / self.mass
        linear_accel = np.array([
            (thrust_world_x - drag_coeff_vel[0]) * inv_mass,
            (thrust_world_y - drag_coeff_vel[1]) * inv_mass,
            (thrust_world_z - gravity_world_z - drag_coeff_vel[2]) * inv_mass
        ])
        
        # Angular acceleration (optimized Euler equation with stability)
        # Avoid cross product computation - use direct formula
        Ixx, Iyy, Izz = self.inertia[0,0], self.inertia[1,1], self.inertia[2,2]
        p, q, r = omega
        
        # Clamp angular velocities to prevent overflow
        p = np.clip(p, -100, 100)
        q = np.clip(q, -100, 100)
        r = np.clip(r, -100, 100)
        
        gyro_x = q * r * (Izz - Iyy)
        gyro_y = p * r * (Ixx - Izz)
        gyro_z = p * q * (Iyy - Ixx)
        
        # Clamp gyroscopic terms to prevent overflow
        gyro_x = np.clip(gyro_x, -10, 10)
        gyro_y = np.clip(gyro_y, -10, 10)
        gyro_z = np.clip(gyro_z, -10, 10)
        
        angular_accel = np.array([
            (moments[0] - gyro_x) / Ixx,
            (moments[1] - gyro_y) / Iyy,
            (moments[2] - gyro_z) / Izz
        ])
        
        # Clamp angular accelerations to prevent instability
        angular_accel = np.clip(angular_accel, -1000, 1000)
        
        # Attitude rate transformation (optimized)
        # Avoid singularity and matrix multiplication
        cos_theta_safe = max(abs(cos_theta), 1e-4) * np.sign(cos_theta)
        tan_theta = sin_theta / cos_theta_safe
        sec_theta = 1.0 / cos_theta_safe
        
        attitude_rates = np.array([
            p + q * sin_phi * tan_theta + r * cos_phi * tan_theta,
            q * cos_phi - r * sin_phi,
            q * sin_phi * sec_theta + r * cos_phi * sec_theta
        ])
        
        # Assemble derivatives using pre-allocated array
        derivatives = np.empty(12)
        derivatives[0:3] = vel                # position derivatives
        derivatives[3:6] = linear_accel       # velocity derivatives
        derivatives[6:9] = attitude_rates     # attitude derivatives
        derivatives[9:12] = angular_accel     # angular velocity derivatives
        
        return derivatives
    
    def _rotation_matrix(self, phi: float, theta: float, psi: float) -> np.ndarray:
        """Compute rotation matrix from body to world frame"""
        cos_phi, sin_phi = np.cos(phi), np.sin(phi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        cos_psi, sin_psi = np.cos(psi), np.sin(psi)
        
        R = np.array([
            [cos_theta * cos_psi, 
             sin_phi * sin_theta * cos_psi - cos_phi * sin_psi,
             cos_phi * sin_theta * cos_psi + sin_phi * sin_psi],
            
            [cos_theta * sin_psi,
             sin_phi * sin_theta * sin_psi + cos_phi * cos_psi,
             cos_phi * sin_theta * sin_psi - sin_phi * cos_psi],
            
            [-sin_theta,
             sin_phi * cos_theta,
             cos_phi * cos_theta]
        ])
        
        return R
    
    def _attitude_rate_transform(self, omega: np.ndarray, 
                               phi: float, theta: float) -> np.ndarray:
        """Transform body angular velocities to Euler angle rates"""
        cos_phi, sin_phi = np.cos(phi), np.sin(phi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        
        # Avoid singularity at theta = ±π/2
        cos_theta = max(abs(cos_theta), 1e-4) * np.sign(cos_theta)
        tan_theta = sin_theta / cos_theta
        
        T = np.array([
            [1, sin_phi * tan_theta, cos_phi * tan_theta],
            [0, cos_phi, -sin_phi],
            [0, sin_phi / cos_theta, cos_phi / cos_theta]
        ])
        
        return T @ omega
    
    def _compute_thrust_and_moments(self, motor_speeds: np.ndarray) -> Tuple[float, np.ndarray]:
        """
        Compute thrust and moments from motor speeds
        
        Motor configuration (X-configuration):
             1
           ╱   ╲
          ╱     ╲
         4   +   2
          ╲     ╱
           ╲   ╱
             3
        
        Args:
            motor_speeds: [w1, w2, w3, w4] in rad/s
            
        Returns:
            Total thrust (N) and moment vector [Mx, My, Mz] (N*m)
        """
        w1, w2, w3, w4 = motor_speeds
        
        # Individual motor thrusts
        f1 = self.thrust_coefficient * w1**2
        f2 = self.thrust_coefficient * w2**2
        f3 = self.thrust_coefficient * w3**2
        f4 = self.thrust_coefficient * w4**2
        
        # Total thrust
        total_thrust = f1 + f2 + f3 + f4
        
        # Individual motor torques (reactive torques)
        tau1 = self.torque_coefficient * w1**2
        tau2 = self.torque_coefficient * w2**2
        tau3 = self.torque_coefficient * w3**2  
        tau4 = self.torque_coefficient * w4**2
        
        # Arm length for moment calculation
        L = self.arm_length
        
        # Moments about body axes (X-configuration)
        # Roll moment (about x-axis)
        Mx = L * (f2 - f4) / np.sqrt(2)
        
        # Pitch moment (about y-axis)  
        My = L * (f3 - f1) / np.sqrt(2)
        
        # Yaw moment (about z-axis)
        Mz = tau1 - tau2 + tau3 - tau4
        
        moments = np.array([Mx, My, Mz])
        
        return total_thrust, moments
    
    def trim_condition(self) -> Dict[str, Any]:
        """
        Compute hover trim condition
        
        Returns:
            Dictionary with trim motor speeds and state
        """
        # Hover requires thrust = weight
        required_thrust = self.mass * self.gravity
        
        # Equal motor speeds for hover
        motor_speed_hover = np.sqrt(required_thrust / (4 * self.thrust_coefficient))
        
        # Trim state (hover at origin)
        trim_state = UAVState()
        
        # Trim inputs
        trim_inputs = np.array([motor_speed_hover] * 4)
        
        return {
            'state': trim_state,
            'inputs': trim_inputs,
            'motor_speed': motor_speed_hover
        }
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get model parameters"""
        return {
            'mass': self.mass,
            'inertia': self.inertia.tolist(),
            'gravity': self.gravity,
            'drag_coefficient': self.drag_coefficient,
            'arm_length': self.arm_length,
            'thrust_coefficient': self.thrust_coefficient,
            'torque_coefficient': self.torque_coefficient
        }