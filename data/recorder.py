"""
Data recording and logging functionality
"""

import numpy as np
import json
import pickle
import h5py
import csv
from typing import Dict, Any, List, Optional, Union
import os
from datetime import datetime
import threading
import queue


class DataRecorder:
    """
    Comprehensive data recorder for simulation results
    """
    
    def __init__(self, max_buffer_size: int = 10000):
        """
        Initialize data recorder
        
        Args:
            max_buffer_size: Maximum number of data points to buffer
        """
        self.max_buffer_size = max_buffer_size
        
        # Data storage
        self.data_buffer = {
            'time': [],
            'states': [],
            'control_inputs': [],
            'control_outputs': [],
            'errors': [],
            'performance_metrics': []
        }
        
        # Recording configuration
        self.recording_active = False
        self.output_directory = "simulation_data"
        self.session_name = None
        
        # Ensure output directory exists
        os.makedirs(self.output_directory, exist_ok=True)
        
        # Thread safety
        self.lock = threading.Lock()
        self.data_queue = queue.Queue()
        
        # Metadata
        self.metadata = {
            'created_at': datetime.now().isoformat(),
            'simulation_parameters': {},
            'vehicle_parameters': {},
            'controller_parameters': {}
        }
    
    def start_recording(self, session_name: str = None):
        """
        Start data recording
        
        Args:
            session_name: Optional session name, auto-generated if None
        """
        with self.lock:
            if self.recording_active:
                print("Recording already active")
                return
            
            if session_name is None:
                session_name = f"sim_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.session_name = session_name
            self.recording_active = True
            self._clear_buffer_unsafe()
            
            print(f"Started recording session: {session_name}")
    
    def stop_recording(self) -> str:
        """
        Stop recording and return session name
        
        Returns:
            Session name
        """
        with self.lock:
            if not self.recording_active:
                print("No active recording")
                return None
            
            self.recording_active = False
            session_name = self.session_name
            
            print(f"Stopped recording session: {session_name}")
            print(f"Recorded {len(self.data_buffer['time'])} data points")
            
            return session_name
    
    def record_step(self, time_val: float, simulation_results: Dict[str, Any]):
        """
        Record data from one simulation step
        
        Args:
            time_val: Current simulation time
            simulation_results: Dictionary with simulation step results
        """
        if not self.recording_active:
            return
        
        try:
            self.data_queue.put((time_val, simulation_results), block=False)
        except queue.Full:
            # If queue is full, process some items
            self._process_queue_batch(10)
            try:
                self.data_queue.put((time_val, simulation_results), block=False)
            except queue.Full:
                print("Warning: Data recording queue overflow")
    
    def _process_queue_batch(self, batch_size: int = 100):
        """Process a batch of queued data points"""
        processed = 0
        while processed < batch_size and not self.data_queue.empty():
            try:
                time_val, results = self.data_queue.get_nowait()
                self._store_data_point(time_val, results)
                processed += 1
            except queue.Empty:
                break
    
    def _store_data_point(self, time_val: float, results: Dict[str, Any]):
        """Store a single data point in buffer"""
        with self.lock:
            # Add time
            self.data_buffer['time'].append(time_val)
            
            # Extract and store state data
            state_data = {}
            for component_name, component_results in results.items():
                if 'state' in component_results:
                    state = component_results['state']
                    if hasattr(state, 'to_dict'):
                        state_data[component_name] = state.to_dict()
                    else:
                        state_data[component_name] = str(state)
                
                # Store other component outputs
                for key, value in component_results.items():
                    if key != 'state' and isinstance(value, (int, float, list, np.ndarray)):
                        if isinstance(value, np.ndarray):
                            value = value.tolist()
                        state_data[f"{component_name}_{key}"] = value
            
            self.data_buffer['states'].append(state_data)
            
            # Store control data (if available)
            control_data = {}
            for component_name, component_results in results.items():
                if 'control' in component_name.lower():
                    for key, value in component_results.items():
                        if isinstance(value, (int, float, list, np.ndarray)):
                            if isinstance(value, np.ndarray):
                                value = value.tolist()
                            control_data[f"{component_name}_{key}"] = value
            
            self.data_buffer['control_inputs'].append(control_data)
            
            # Maintain buffer size
            if len(self.data_buffer['time']) > self.max_buffer_size:
                for key in self.data_buffer:
                    if isinstance(self.data_buffer[key], list):
                        self.data_buffer[key] = self.data_buffer[key][-self.max_buffer_size:]
    
    def flush_queue(self):
        """Process all queued data"""
        while not self.data_queue.empty():
            try:
                time_val, results = self.data_queue.get_nowait()
                self._store_data_point(time_val, results)
            except queue.Empty:
                break
    
    def _clear_buffer_unsafe(self):
        """Clear all buffered data (unsafe - must be called with lock held)"""
        # Reinitialize all buffer lists
        self.data_buffer = {
            'time': [],
            'states': [],
            'control_inputs': [],
            'control_outputs': [],
            'errors': [],
            'performance_metrics': []
        }
    
    def clear_buffer(self):
        """Clear all buffered data"""
        with self.lock:
            self._clear_buffer_unsafe()
    
    def set_metadata(self, **kwargs):
        """Set metadata for the recording"""
        self.metadata.update(kwargs)
    
    def save_data(self, filename: str = None, format: str = 'hdf5') -> str:
        """
        Save recorded data to file
        
        Args:
            filename: Output filename (auto-generated if None)
            format: File format ('hdf5', 'json', 'pickle', 'csv')
            
        Returns:
            Saved filename
        """
        # Flush any remaining queued data
        self.flush_queue()
        
        if filename is None:
            session_name = self.session_name or "unnamed_session"
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{session_name}_{timestamp}"
        
        # Add file extension based on format
        if format == 'hdf5':
            full_filename = os.path.join(self.output_directory, f"{filename}.h5")
            self._save_hdf5(full_filename)
        elif format == 'json':
            full_filename = os.path.join(self.output_directory, f"{filename}.json")
            self._save_json(full_filename)
        elif format == 'pickle':
            full_filename = os.path.join(self.output_directory, f"{filename}.pkl")
            self._save_pickle(full_filename)
        elif format == 'csv':
            full_filename = os.path.join(self.output_directory, f"{filename}.csv")
            self._save_csv(full_filename)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        print(f"Data saved to: {full_filename}")
        return full_filename
    
    def _save_hdf5(self, filename: str):
        """Save data in HDF5 format"""
        with h5py.File(filename, 'w') as f:
            # Save metadata
            metadata_group = f.create_group('metadata')
            for key, value in self.metadata.items():
                if isinstance(value, dict):
                    subgroup = metadata_group.create_group(key)
                    for subkey, subvalue in value.items():
                        subgroup.attrs[subkey] = str(subvalue)
                else:
                    metadata_group.attrs[key] = str(value)
            
            # Save time data
            if self.data_buffer['time']:
                f.create_dataset('time', data=np.array(self.data_buffer['time']))
            
            # Save state data
            if self.data_buffer['states']:
                states_group = f.create_group('states')
                # Find all unique keys across all state entries
                all_keys = set()
                for state_dict in self.data_buffer['states']:
                    all_keys.update(state_dict.keys())
                
                # Create datasets for each key
                for key in all_keys:
                    values = []
                    for state_dict in self.data_buffer['states']:
                        if key in state_dict:
                            value = state_dict[key]
                            if isinstance(value, dict):
                                # For nested dictionaries, store as string
                                values.append(str(value))
                            else:
                                values.append(value)
                        else:
                            values.append(None)
                    
                    # Convert to appropriate numpy array
                    try:
                        data_array = np.array(values)
                        states_group.create_dataset(key, data=data_array)
                    except:
                        # If conversion fails, store as strings
                        string_values = [str(v) for v in values]
                        states_group.create_dataset(key, data=string_values)
    
    def _save_json(self, filename: str):
        """Save data in JSON format"""
        # Convert numpy arrays to lists for JSON serialization
        json_data = {
            'metadata': self.metadata,
            'time': self.data_buffer['time'],
            'states': self.data_buffer['states'],
            'control_inputs': self.data_buffer['control_inputs']
        }
        
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2, default=self._json_serializer)
    
    def _save_pickle(self, filename: str):
        """Save data in pickle format"""
        data = {
            'metadata': self.metadata,
            'data_buffer': self.data_buffer
        }
        
        with open(filename, 'wb') as f:
            pickle.dump(data, f)
    
    def _save_csv(self, filename: str):
        """Save data in CSV format (flattened)"""
        if not self.data_buffer['time']:
            print("No data to save")
            return
        
        # Flatten data for CSV
        rows = []
        headers = ['time']
        
        # Get all possible field names
        all_fields = set()
        for state_dict in self.data_buffer['states']:
            all_fields.update(state_dict.keys())
        
        headers.extend(sorted(all_fields))
        
        # Create rows
        for i, time_val in enumerate(self.data_buffer['time']):
            row = [time_val]
            
            state_dict = self.data_buffer['states'][i] if i < len(self.data_buffer['states']) else {}
            
            for field in sorted(all_fields):
                value = state_dict.get(field, '')
                if isinstance(value, (list, np.ndarray)):
                    value = ','.join(map(str, value))
                elif isinstance(value, dict):
                    value = json.dumps(value)
                row.append(str(value))
            
            rows.append(row)
        
        # Write CSV
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerows(rows)
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for numpy arrays"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        else:
            return str(obj)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get recording statistics"""
        return {
            'recording_active': self.recording_active,
            'session_name': self.session_name,
            'data_points': len(self.data_buffer['time']),
            'queue_size': self.data_queue.qsize(),
            'buffer_usage': f"{len(self.data_buffer['time'])}/{self.max_buffer_size}",
            'time_span': (
                f"{self.data_buffer['time'][0]:.3f} - {self.data_buffer['time'][-1]:.3f} s"
                if self.data_buffer['time'] else "No data"
            )
        }
    
    def load_data(self, filename: str) -> Dict[str, Any]:
        """
        Load previously saved data
        
        Args:
            filename: Path to data file
            
        Returns:
            Loaded data dictionary
        """
        if filename.endswith('.h5'):
            return self._load_hdf5(filename)
        elif filename.endswith('.json'):
            return self._load_json(filename)
        elif filename.endswith('.pkl'):
            return self._load_pickle(filename)
        else:
            raise ValueError(f"Unsupported file format: {filename}")
    
    def _load_hdf5(self, filename: str) -> Dict[str, Any]:
        """Load data from HDF5 file"""
        data = {}
        
        with h5py.File(filename, 'r') as f:
            # Load metadata
            if 'metadata' in f:
                metadata = {}
                for key, value in f['metadata'].attrs.items():
                    metadata[key] = value
                data['metadata'] = metadata
            
            # Load time
            if 'time' in f:
                data['time'] = f['time'][:]
            
            # Load states
            if 'states' in f:
                states = {}
                for key in f['states'].keys():
                    states[key] = f['states'][key][:]
                data['states'] = states
        
        return data
    
    def _load_json(self, filename: str) -> Dict[str, Any]:
        """Load data from JSON file"""
        with open(filename, 'r') as f:
            return json.load(f)
    
    def _load_pickle(self, filename: str) -> Dict[str, Any]:
        """Load data from pickle file"""
        with open(filename, 'rb') as f:
            return pickle.load(f)