"""
Data analysis and performance metrics
"""

import numpy as np
# import matplotlib.pyplot as plt  # Disabled due to compatibility issues
from typing import Dict, List, Tuple, Any, Optional
from scipy import signal
from scipy.stats import describe
# import pandas as pd  # Optional for now


class DataAnalyzer:
    """
    Analysis tools for simulation data
    """
    
    def __init__(self, data: Dict[str, Any] = None):
        """
        Initialize analyzer
        
        Args:
            data: Loaded simulation data dictionary
        """
        self.data = data
        self.analysis_results = {}
    
    def load_data(self, data: Dict[str, Any]):
        """Load data for analysis"""
        self.data = data
        self.analysis_results = {}
    
    def compute_trajectory_metrics(self, reference_trajectory: np.ndarray = None) -> Dict[str, float]:
        """
        Compute trajectory following metrics
        
        Args:
            reference_trajectory: Reference trajectory (N x 3)
            
        Returns:
            Dictionary of trajectory metrics
        """
        if not self.data or 'time' not in self.data:
            return {}
        
        # Extract position data
        positions = self._extract_position_data()
        if positions is None:
            return {}
        
        metrics = {}
        
        # Basic trajectory statistics
        metrics['trajectory_length'] = self._compute_trajectory_length(positions)
        metrics['max_velocity'] = self._compute_max_velocity(positions, self.data['time'])
        metrics['avg_velocity'] = self._compute_avg_velocity(positions, self.data['time'])
        
        # Tracking performance (if reference provided)
        if reference_trajectory is not None:
            tracking_metrics = self._compute_tracking_metrics(positions, reference_trajectory)
            metrics.update(tracking_metrics)
        
        # Smoothness metrics
        smoothness_metrics = self._compute_smoothness_metrics(positions, self.data['time'])
        metrics.update(smoothness_metrics)
        
        self.analysis_results['trajectory_metrics'] = metrics
        return metrics
    
    def compute_control_performance(self) -> Dict[str, float]:
        """
        Compute control system performance metrics
        
        Returns:
            Dictionary of control performance metrics
        """
        if not self.data:
            return {}
        
        metrics = {}
        
        # Extract attitude data
        attitudes = self._extract_attitude_data()
        if attitudes is not None:
            attitude_metrics = self._compute_attitude_performance(attitudes, self.data['time'])
            metrics.update(attitude_metrics)
        
        # Extract control effort data
        control_data = self._extract_control_data()
        if control_data is not None:
            control_metrics = self._compute_control_effort_metrics(control_data, self.data['time'])
            metrics.update(control_metrics)
        
        self.analysis_results['control_performance'] = metrics
        return metrics
    
    def compute_stability_metrics(self) -> Dict[str, float]:
        """
        Compute stability and robustness metrics
        
        Returns:
            Dictionary of stability metrics
        """
        if not self.data:
            return {}
        
        metrics = {}
        
        # Extract state data
        positions = self._extract_position_data()
        attitudes = self._extract_attitude_data()
        
        if positions is not None:
            # Position stability
            pos_stability = self._analyze_stability(positions, self.data['time'])
            metrics.update({f'position_{k}': v for k, v in pos_stability.items()})
        
        if attitudes is not None:
            # Attitude stability
            att_stability = self._analyze_stability(attitudes, self.data['time'])
            metrics.update({f'attitude_{k}': v for k, v in att_stability.items()})
        
        self.analysis_results['stability_metrics'] = metrics
        return metrics
    
    def compute_frequency_analysis(self, signal_name: str = 'position') -> Dict[str, Any]:
        """
        Perform frequency domain analysis
        
        Args:
            signal_name: Name of signal to analyze
            
        Returns:
            Dictionary with frequency analysis results
        """
        if not self.data or 'time' not in self.data:
            return {}
        
        # Get signal data
        if signal_name == 'position':
            signal_data = self._extract_position_data()
        elif signal_name == 'attitude':
            signal_data = self._extract_attitude_data()
        else:
            return {}
        
        if signal_data is None:
            return {}
        
        time_data = np.array(self.data['time'])
        dt = np.mean(np.diff(time_data))
        fs = 1.0 / dt
        
        results = {}
        
        # Analyze each axis
        axis_names = ['x', 'y', 'z'] if signal_name == 'position' else ['roll', 'pitch', 'yaw']
        
        for i, axis in enumerate(axis_names):
            if i < signal_data.shape[1]:
                signal_axis = signal_data[:, i]
                
                # Power spectral density
                frequencies, psd = signal.welch(signal_axis, fs=fs, nperseg=min(256, len(signal_axis)//4))
                
                # Find dominant frequency
                dominant_freq_idx = np.argmax(psd[1:]) + 1  # Skip DC component
                dominant_frequency = frequencies[dominant_freq_idx]
                
                results[f'{axis}_dominant_freq'] = dominant_frequency
                results[f'{axis}_max_psd'] = psd[dominant_freq_idx]
                
                # Frequency content analysis
                low_freq_power = np.sum(psd[frequencies < 1.0])
                high_freq_power = np.sum(psd[frequencies >= 1.0])
                total_power = np.sum(psd)
                
                results[f'{axis}_low_freq_ratio'] = low_freq_power / total_power if total_power > 0 else 0
                results[f'{axis}_high_freq_ratio'] = high_freq_power / total_power if total_power > 0 else 0
        
        self.analysis_results[f'{signal_name}_frequency_analysis'] = results
        return results
    
    def generate_performance_report(self, filename: str = None) -> str:
        """
        Generate comprehensive performance report
        
        Args:
            filename: Output filename for report
            
        Returns:
            Report text
        """
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("UAV SIMULATION PERFORMANCE REPORT")
        report_lines.append("=" * 60)
        
        # Compute all metrics
        trajectory_metrics = self.compute_trajectory_metrics()
        control_metrics = self.compute_control_performance()
        stability_metrics = self.compute_stability_metrics()
        
        # Trajectory Performance
        if trajectory_metrics:
            report_lines.append("\n--- TRAJECTORY PERFORMANCE ---")
            for key, value in trajectory_metrics.items():
                if isinstance(value, float):
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value:.4f}")
                else:
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value}")
        
        # Control Performance
        if control_metrics:
            report_lines.append("\n--- CONTROL PERFORMANCE ---")
            for key, value in control_metrics.items():
                if isinstance(value, float):
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value:.4f}")
                else:
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value}")
        
        # Stability Metrics
        if stability_metrics:
            report_lines.append("\n--- STABILITY METRICS ---")
            for key, value in stability_metrics.items():
                if isinstance(value, float):
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value:.4f}")
                else:
                    report_lines.append(f"{key.replace('_', ' ').title()}: {value}")
        
        report_text = "\n".join(report_lines)
        
        # Save to file if requested
        if filename:
            with open(filename, 'w') as f:
                f.write(report_text)
            print(f"Report saved to: {filename}")
        
        return report_text
    
    def plot_analysis_results(self, save_dir: str = None):
        """
        Generate analysis plots (disabled due to matplotlib compatibility issues)
        
        Args:
            save_dir: Directory to save plots
        """
        print("Plotting disabled due to matplotlib compatibility issues")
        return
        
        # All matplotlib plotting code disabled due to compatibility issues
    
    # Helper methods for data extraction and analysis
    def _extract_position_data(self) -> Optional[np.ndarray]:
        """Extract position data from loaded data"""
        if 'states' not in self.data:
            return None
        
        # Try different possible data structures
        states = self.data['states']
        
        if isinstance(states, dict):
            # HDF5 format - look for position keys
            for key in states.keys():
                if 'position' in key.lower():
                    data = states[key]
                    if isinstance(data, np.ndarray) and data.shape[1] >= 3:
                        return data[:, :3]
        elif isinstance(states, list):
            # JSON format - extract from state dictionaries
            positions = []
            for state_dict in states:
                for key, value in state_dict.items():
                    if 'position' in key.lower() and isinstance(value, (list, np.ndarray)):
                        if len(value) >= 3:
                            positions.append(value[:3])
                            break
            if positions:
                return np.array(positions)
        
        return None
    
    def _extract_attitude_data(self) -> Optional[np.ndarray]:
        """Extract attitude data from loaded data"""
        if 'states' not in self.data:
            return None
        
        states = self.data['states']
        
        if isinstance(states, dict):
            for key in states.keys():
                if 'attitude' in key.lower():
                    data = states[key]
                    if isinstance(data, np.ndarray) and data.shape[1] >= 3:
                        return data[:, :3]
        elif isinstance(states, list):
            attitudes = []
            for state_dict in states:
                for key, value in state_dict.items():
                    if 'attitude' in key.lower() and isinstance(value, (list, np.ndarray)):
                        if len(value) >= 3:
                            attitudes.append(value[:3])
                            break
            if attitudes:
                return np.array(attitudes)
        
        return None
    
    def _extract_control_data(self) -> Optional[np.ndarray]:
        """Extract control input data"""
        if 'control_inputs' not in self.data:
            return None
        
        control_inputs = self.data['control_inputs']
        if isinstance(control_inputs, list) and control_inputs:
            # Extract motor commands or control signals
            control_data = []
            for control_dict in control_inputs:
                for key, value in control_dict.items():
                    if isinstance(value, (list, np.ndarray)) and len(value) >= 4:
                        control_data.append(value[:4])
                        break
            if control_data:
                return np.array(control_data)
        
        return None
    
    def _compute_trajectory_length(self, positions: np.ndarray) -> float:
        """Compute total trajectory length"""
        if len(positions) < 2:
            return 0.0
        
        diffs = np.diff(positions, axis=0)
        distances = np.linalg.norm(diffs, axis=1)
        return np.sum(distances)
    
    def _compute_velocities(self, positions: np.ndarray, times: np.ndarray) -> np.ndarray:
        """Compute velocities from position data"""
        dt = np.diff(times)
        dp = np.diff(positions, axis=0)
        velocities = dp / dt[:, np.newaxis]
        return velocities
    
    def _compute_max_velocity(self, positions: np.ndarray, times: np.ndarray) -> float:
        """Compute maximum velocity magnitude"""
        velocities = self._compute_velocities(positions, times)
        velocity_magnitudes = np.linalg.norm(velocities, axis=1)
        return np.max(velocity_magnitudes)
    
    def _compute_avg_velocity(self, positions: np.ndarray, times: np.ndarray) -> float:
        """Compute average velocity magnitude"""
        velocities = self._compute_velocities(positions, times)
        velocity_magnitudes = np.linalg.norm(velocities, axis=1)
        return np.mean(velocity_magnitudes)
    
    def _compute_tracking_metrics(self, actual: np.ndarray, reference: np.ndarray) -> Dict[str, float]:
        """Compute trajectory tracking metrics"""
        # Interpolate reference to match actual trajectory length
        if len(reference) != len(actual):
            from scipy.interpolate import interp1d
            ref_indices = np.linspace(0, len(reference)-1, len(reference))
            actual_indices = np.linspace(0, len(reference)-1, len(actual))
            
            interp_funcs = []
            for i in range(reference.shape[1]):
                interp_funcs.append(interp1d(ref_indices, reference[:, i], 
                                           kind='linear', fill_value='extrapolate'))
            
            reference_interp = np.column_stack([f(actual_indices) for f in interp_funcs])
        else:
            reference_interp = reference
        
        # Compute tracking errors
        errors = actual - reference_interp
        
        metrics = {}
        metrics['rmse_x'] = np.sqrt(np.mean(errors[:, 0]**2))
        metrics['rmse_y'] = np.sqrt(np.mean(errors[:, 1]**2))
        metrics['rmse_z'] = np.sqrt(np.mean(errors[:, 2]**2))
        metrics['rmse_total'] = np.sqrt(np.mean(np.linalg.norm(errors, axis=1)**2))
        
        metrics['max_error_x'] = np.max(np.abs(errors[:, 0]))
        metrics['max_error_y'] = np.max(np.abs(errors[:, 1]))
        metrics['max_error_z'] = np.max(np.abs(errors[:, 2]))
        metrics['max_error_total'] = np.max(np.linalg.norm(errors, axis=1))
        
        return metrics
    
    def _compute_smoothness_metrics(self, positions: np.ndarray, times: np.ndarray) -> Dict[str, float]:
        """Compute trajectory smoothness metrics"""
        if len(positions) < 3:
            return {}
        
        # Compute accelerations (second derivative)
        velocities = self._compute_velocities(positions, times)
        accelerations = self._compute_velocities(velocities, times[1:])
        
        metrics = {}
        
        # Acceleration statistics
        accel_magnitudes = np.linalg.norm(accelerations, axis=1)
        metrics['max_acceleration'] = np.max(accel_magnitudes)
        metrics['avg_acceleration'] = np.mean(accel_magnitudes)
        metrics['acceleration_std'] = np.std(accel_magnitudes)
        
        # Jerk (third derivative)
        if len(accelerations) > 1:
            jerks = self._compute_velocities(accelerations, times[2:])
            jerk_magnitudes = np.linalg.norm(jerks, axis=1)
            metrics['max_jerk'] = np.max(jerk_magnitudes)
            metrics['avg_jerk'] = np.mean(jerk_magnitudes)
        
        return metrics
    
    def _compute_attitude_performance(self, attitudes: np.ndarray, times: np.ndarray) -> Dict[str, float]:
        """Compute attitude control performance"""
        metrics = {}
        
        # Attitude statistics
        for i, axis in enumerate(['roll', 'pitch', 'yaw']):
            attitude_axis = attitudes[:, i]
            metrics[f'{axis}_max'] = np.degrees(np.max(np.abs(attitude_axis)))
            metrics[f'{axis}_std'] = np.degrees(np.std(attitude_axis))
            metrics[f'{axis}_range'] = np.degrees(np.ptp(attitude_axis))
        
        # Angular velocity (attitude rate)
        if len(attitudes) > 1:
            angular_rates = self._compute_velocities(attitudes, times)
            for i, axis in enumerate(['roll_rate', 'pitch_rate', 'yaw_rate']):
                rate_axis = angular_rates[:, i]
                metrics[f'{axis}_max'] = np.degrees(np.max(np.abs(rate_axis)))
                metrics[f'{axis}_std'] = np.degrees(np.std(rate_axis))
        
        return metrics
    
    def _compute_control_effort_metrics(self, control_data: np.ndarray, times: np.ndarray) -> Dict[str, float]:
        """Compute control effort metrics"""
        metrics = {}
        
        # Control signal statistics
        for i in range(min(4, control_data.shape[1])):
            motor_data = control_data[:, i]
            metrics[f'motor_{i+1}_avg'] = np.mean(motor_data)
            metrics[f'motor_{i+1}_max'] = np.max(motor_data)
            metrics[f'motor_{i+1}_std'] = np.std(motor_data)
        
        # Total control effort
        total_effort = np.sum(control_data, axis=1)
        metrics['total_control_effort_avg'] = np.mean(total_effort)
        metrics['total_control_effort_max'] = np.max(total_effort)
        
        # Control variation (smoothness)
        if len(control_data) > 1:
            control_rates = self._compute_velocities(control_data, times)
            control_rate_magnitudes = np.linalg.norm(control_rates, axis=1)
            metrics['control_variation_avg'] = np.mean(control_rate_magnitudes)
            metrics['control_variation_max'] = np.max(control_rate_magnitudes)
        
        return metrics
    
    def _analyze_stability(self, signal_data: np.ndarray, times: np.ndarray) -> Dict[str, float]:
        """Analyze signal stability"""
        metrics = {}
        
        # Overall stability metrics
        signal_magnitudes = np.linalg.norm(signal_data, axis=1)
        
        # Lyapunov-like stability indicator (decreasing trend in magnitude)
        if len(signal_magnitudes) > 10:
            # Fit linear trend to signal magnitude
            coeffs = np.polyfit(times, signal_magnitudes, 1)
            metrics['stability_trend'] = coeffs[0]  # Negative = stable
        
        # Oscillation analysis
        for i in range(signal_data.shape[1]):
            axis_data = signal_data[:, i]
            
            # Find local maxima and minima
            from scipy.signal import find_peaks
            peaks, _ = find_peaks(axis_data)
            valleys, _ = find_peaks(-axis_data)
            
            if len(peaks) > 1 and len(valleys) > 1:
                # Oscillation frequency estimate
                peak_intervals = np.diff(times[peaks])
                valley_intervals = np.diff(times[valleys])
                
                if len(peak_intervals) > 0:
                    metrics[f'axis_{i}_oscillation_freq'] = 1.0 / np.mean(peak_intervals)
                
                # Damping estimate (decrease in peak amplitudes)
                if len(peaks) > 2:
                    peak_amplitudes = axis_data[peaks]
                    if len(peak_amplitudes) > 1:
                        damping_trend = np.polyfit(range(len(peak_amplitudes)), 
                                                 np.log(np.abs(peak_amplitudes) + 1e-10), 1)[0]
                        metrics[f'axis_{i}_damping_ratio'] = -damping_trend
        
        return metrics