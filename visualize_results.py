#!/usr/bin/env python3
"""
Visualization script for UAV simulation results
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import json
import h5py
import os
from datetime import datetime

def load_simulation_data(json_file):
    """Load simulation data from JSON file"""
    with open(json_file, 'r') as f:
        data = json.load(f)
    return data

def create_visualization(data, output_file='simulation_visualization.png'):
    """Create comprehensive visualization of simulation results"""
    
    # Set up the figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle('UAV Hover Simulation Results', fontsize=16)
    
    # Extract time and position data
    time_data = data.get('time', [])
    states = data.get('states', [])
    
    if not time_data or not states:
        print("No data to visualize")
        return
    
    # Extract position, velocity, and attitude data
    positions = {'x': [], 'y': [], 'z': []}
    velocities = {'vx': [], 'vy': [], 'vz': []}
    attitudes = {'roll': [], 'pitch': [], 'yaw': []}
    
    for state in states:
        # Position
        positions['x'].append(state.get('dynamics_position_x', 0))
        positions['y'].append(state.get('dynamics_position_y', 0))
        positions['z'].append(state.get('dynamics_position_z', 0))
        
        # Velocity
        velocities['vx'].append(state.get('dynamics_velocity_x', 0))
        velocities['vy'].append(state.get('dynamics_velocity_y', 0))
        velocities['vz'].append(state.get('dynamics_velocity_z', 0))
        
        # Attitude (convert from radians to degrees)
        attitudes['roll'].append(np.degrees(state.get('dynamics_attitude_roll', 0)))
        attitudes['pitch'].append(np.degrees(state.get('dynamics_attitude_pitch', 0)))
        attitudes['yaw'].append(np.degrees(state.get('dynamics_attitude_yaw', 0)))
    
    # Plot 1: Position vs Time
    axes[0, 0].plot(time_data, positions['x'], 'r-', label='X', linewidth=2)
    axes[0, 0].plot(time_data, positions['y'], 'g-', label='Y', linewidth=2)
    axes[0, 0].plot(time_data, positions['z'], 'b-', label='Z', linewidth=2)
    axes[0, 0].axhline(y=-1, color='k', linestyle='--', alpha=0.5, label='Target Z')
    axes[0, 0].set_xlabel('Time (s)')
    axes[0, 0].set_ylabel('Position (m)')
    axes[0, 0].set_title('Position vs Time')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: XY Trajectory (Top View)
    axes[0, 1].plot(positions['x'], positions['y'], 'b-', linewidth=2, alpha=0.7, label='Trajectory')
    axes[0, 1].scatter([0], [0], color='red', s=100, label='Target', marker='x')
    axes[0, 1].scatter([positions['x'][0]], [positions['y'][0]], 
                      color='green', s=100, label='Start', marker='o')
    axes[0, 1].scatter([positions['x'][-1]], [positions['y'][-1]], 
                      color='orange', s=100, label='End', marker='s')
    axes[0, 1].set_xlabel('X (m)')
    axes[0, 1].set_ylabel('Y (m)')
    axes[0, 1].set_title('XY Trajectory (Top View)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axis('equal')
    
    # Plot 3: Velocity vs Time
    axes[0, 2].plot(time_data, velocities['vx'], 'r-', label='Vx', linewidth=2)
    axes[0, 2].plot(time_data, velocities['vy'], 'g-', label='Vy', linewidth=2)
    axes[0, 2].plot(time_data, velocities['vz'], 'b-', label='Vz', linewidth=2)
    axes[0, 2].set_xlabel('Time (s)')
    axes[0, 2].set_ylabel('Velocity (m/s)')
    axes[0, 2].set_title('Velocity vs Time')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # Plot 4: Attitude vs Time
    axes[1, 0].plot(time_data, attitudes['roll'], 'r-', label='Roll', linewidth=2)
    axes[1, 0].plot(time_data, attitudes['pitch'], 'g-', label='Pitch', linewidth=2)
    axes[1, 0].plot(time_data, attitudes['yaw'], 'b-', label='Yaw', linewidth=2)
    axes[1, 0].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[1, 0].set_xlabel('Time (s)')
    axes[1, 0].set_ylabel('Attitude (degrees)')
    axes[1, 0].set_title('Attitude vs Time')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 5: Position Error
    target_pos = np.array([0, 0, -1])
    pos_array = np.array([positions['x'], positions['y'], positions['z']]).T
    position_errors = np.linalg.norm(pos_array - target_pos, axis=1)
    
    axes[1, 1].plot(time_data, position_errors, 'purple', linewidth=2)
    axes[1, 1].set_xlabel('Time (s)')
    axes[1, 1].set_ylabel('Position Error (m)')
    axes[1, 1].set_title('Position Error vs Time')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Plot 6: Attitude Error
    attitude_errors = np.sqrt(np.array(attitudes['roll'])**2 + 
                             np.array(attitudes['pitch'])**2 + 
                             np.array(attitudes['yaw'])**2)
    
    axes[1, 2].plot(time_data, attitude_errors, 'orange', linewidth=2)
    axes[1, 2].set_xlabel('Time (s)')
    axes[1, 2].set_ylabel('Attitude Error (degrees)')
    axes[1, 2].set_title('Total Attitude Error vs Time')
    axes[1, 2].grid(True, alpha=0.3)
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Visualization saved to: {output_file}")
    
    # Print summary statistics
    print("\n" + "="*50)
    print("SIMULATION VISUALIZATION SUMMARY")
    print("="*50)
    print(f"Final Position: [{positions['x'][-1]:.3f}, {positions['y'][-1]:.3f}, {positions['z'][-1]:.3f}] m")
    print(f"Final Position Error: {position_errors[-1]:.3f} m")
    print(f"Final Attitude Error: {attitude_errors[-1]:.1f} degrees")
    print(f"Max Position Error: {max(position_errors):.3f} m")
    print(f"Max Attitude Error: {max(attitude_errors):.1f} degrees")
    print(f"Simulation Duration: {time_data[-1]:.1f} seconds")
    print(f"Number of Data Points: {len(time_data)}")

def main():
    """Main visualization function"""
    
    # Find the latest simulation data file
    data_dir = "simulation_data"
    if not os.path.exists(data_dir):
        print("No simulation data directory found!")
        return
    
    json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
    if not json_files:
        print("No JSON data files found!")
        return
    
    # Use the most recent file
    latest_file = max(json_files, key=lambda f: os.path.getctime(os.path.join(data_dir, f)))
    json_path = os.path.join(data_dir, latest_file)
    
    print(f"Loading simulation data from: {json_path}")
    
    # Load and visualize data
    try:
        data = load_simulation_data(json_path)
        create_visualization(data, 'uav_simulation_results.png')
        
        print("\nVisualization complete!")
        print("Generated file: uav_simulation_results.png")
        
    except Exception as e:
        print(f"Error creating visualization: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()