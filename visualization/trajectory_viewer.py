"""
3D trajectory visualization and animation
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.animation import FuncAnimation
import matplotlib.patches as patches
from typing import Dict, List, Optional, Tuple, Any
import threading
import queue


class TrajectoryViewer3D:
    """
    3D trajectory viewer with real-time animation
    """
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize 3D trajectory viewer
        
        Args:
            figsize: Figure size (width, height)
        """
        self.figsize = figsize
        
        # Setup 3D plot
        self.fig = plt.figure(figsize=figsize)
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # Trajectory data
        self.trajectory_data = {
            'time': [],
            'position': [],
            'attitude': []
        }
        
        # Plot elements
        self.trajectory_line = None
        self.uav_marker = None
        self.attitude_arrows = []
        self.reference_line = None
        
        # Animation
        self.animation = None
        self.is_running = False
        self.current_index = 0
        
        # Data queue for real-time updates
        self.data_queue = queue.Queue()
        
        # Visualization settings
        self.trail_length = 100  # Number of points to show in trail
        self.uav_size = 0.2
        self.arrow_length = 0.3
        
        self._setup_plot()
    
    def _setup_plot(self):
        """Setup 3D plot appearance"""
        self.ax.set_xlabel('X (m)')
        self.ax.set_ylabel('Y (m)')
        self.ax.set_zlabel('Z (m)')
        self.ax.set_title('UAV 3D Trajectory')
        
        # Set equal aspect ratio
        self.ax.set_box_aspect([1,1,1])
        
        # Initialize plot elements
        self.trajectory_line, = self.ax.plot([], [], [], 'b-', 
                                           linewidth=2, label='Trajectory')
        
        self.uav_marker, = self.ax.plot([], [], [], 'ro', 
                                       markersize=8, label='UAV')
        
        # Attitude arrows (roll, pitch, yaw axes)
        colors = ['red', 'green', 'blue']
        labels = ['X (Roll)', 'Y (Pitch)', 'Z (Yaw)']
        
        for i, (color, label) in enumerate(zip(colors, labels)):
            arrow = self.ax.quiver(0, 0, 0, 0, 0, 0, 
                                 color=color, arrow_length_ratio=0.2,
                                 linewidth=2, label=label)
            self.attitude_arrows.append(arrow)
        
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)
    
    def add_state_data(self, time_val: float, position: np.ndarray, 
                      attitude: np.ndarray = None):
        """
        Add state data point
        
        Args:
            time_val: Time value
            position: Position [x, y, z]
            attitude: Attitude [roll, pitch, yaw] (optional)
        """
        try:
            self.data_queue.put((time_val, position.copy(), 
                               attitude.copy() if attitude is not None else None), 
                               block=False)
        except queue.Full:
            # Remove oldest data if queue is full
            try:
                self.data_queue.get_nowait()
                self.data_queue.put((time_val, position.copy(), 
                                   attitude.copy() if attitude is not None else None), 
                                   block=False)
            except queue.Empty:
                pass
    
    def set_reference_trajectory(self, positions: np.ndarray):
        """
        Set reference trajectory to display
        
        Args:
            positions: Array of reference positions (N x 3)
        """
        if self.reference_line:
            self.reference_line.remove()
        
        if len(positions) > 0:
            self.reference_line, = self.ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                                              'g--', linewidth=2, alpha=0.7, 
                                              label='Reference')
            self.ax.legend()
    
    def _update_animation(self, frame):
        """Update animation frame"""
        # Process all queued data
        new_data = False
        while True:
            try:
                time_val, position, attitude = self.data_queue.get_nowait()
                
                # Add to trajectory data
                self.trajectory_data['time'].append(time_val)
                self.trajectory_data['position'].append(position)
                self.trajectory_data['attitude'].append(attitude)
                
                # Limit data length for performance
                max_points = 1000
                if len(self.trajectory_data['time']) > max_points:
                    self.trajectory_data['time'] = self.trajectory_data['time'][-max_points:]
                    self.trajectory_data['position'] = self.trajectory_data['position'][-max_points:]
                    self.trajectory_data['attitude'] = self.trajectory_data['attitude'][-max_points:]
                
                new_data = True
                
            except queue.Empty:
                break
        
        if not new_data or len(self.trajectory_data['position']) == 0:
            return []
        
        positions = np.array(self.trajectory_data['position'])
        
        # Update trajectory line (show trail)
        trail_start = max(0, len(positions) - self.trail_length)
        trail_positions = positions[trail_start:]
        
        self.trajectory_line.set_data_3d(trail_positions[:, 0], 
                                       trail_positions[:, 1],
                                       trail_positions[:, 2])
        
        # Update current UAV position
        current_pos = positions[-1]
        self.uav_marker.set_data_3d([current_pos[0]], [current_pos[1]], [current_pos[2]])
        
        # Update attitude arrows if attitude data is available
        if (self.trajectory_data['attitude'][-1] is not None and 
            len(self.attitude_arrows) == 3):
            
            attitude = self.trajectory_data['attitude'][-1]
            
            # Compute rotation matrix from Euler angles
            phi, theta, psi = attitude
            R = self._euler_to_rotation_matrix(phi, theta, psi)
            
            # Body frame unit vectors
            body_axes = np.array([[1, 0, 0], [0, 1, 0], [0, 0, 1]])
            
            # Transform to world frame
            world_axes = R @ body_axes.T
            
            # Remove old arrows
            for arrow in self.attitude_arrows:
                arrow.remove()
            
            # Create new arrows
            self.attitude_arrows = []
            colors = ['red', 'green', 'blue']
            
            for i, color in enumerate(colors):
                arrow = self.ax.quiver(current_pos[0], current_pos[1], current_pos[2],
                                     world_axes[0, i] * self.arrow_length,
                                     world_axes[1, i] * self.arrow_length, 
                                     world_axes[2, i] * self.arrow_length,
                                     color=color, arrow_length_ratio=0.2, linewidth=2)
                self.attitude_arrows.append(arrow)
        
        # Auto-scale view
        if len(positions) > 0:
            margin = 1.0
            x_min, x_max = positions[:, 0].min() - margin, positions[:, 0].max() + margin
            y_min, y_max = positions[:, 1].min() - margin, positions[:, 1].max() + margin
            z_min, z_max = positions[:, 2].min() - margin, positions[:, 2].max() + margin
            
            self.ax.set_xlim(x_min, x_max)
            self.ax.set_ylim(y_min, y_max)
            self.ax.set_zlim(z_min, z_max)
        
        return []
    
    def _euler_to_rotation_matrix(self, phi: float, theta: float, psi: float) -> np.ndarray:
        """Convert Euler angles to rotation matrix"""
        cos_phi, sin_phi = np.cos(phi), np.sin(phi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        cos_psi, sin_psi = np.cos(psi), np.sin(psi)
        
        R = np.array([
            [cos_theta * cos_psi, 
             sin_phi * sin_theta * cos_psi - cos_phi * sin_psi,
             cos_phi * sin_theta * cos_psi + sin_phi * sin_psi],
            
            [cos_theta * sin_psi,
             sin_phi * sin_theta * sin_psi + cos_phi * cos_psi,
             cos_phi * sin_theta * sin_psi - sin_phi * cos_psi],
            
            [-sin_theta,
             sin_phi * cos_theta,
             cos_phi * cos_theta]
        ])
        
        return R
    
    def start_animation(self, interval: int = 50):
        """
        Start real-time animation
        
        Args:
            interval: Animation update interval (ms)
        """
        if self.is_running:
            return
        
        self.is_running = True
        self.animation = FuncAnimation(self.fig, self._update_animation,
                                     interval=interval, blit=False,
                                     cache_frame_data=False)
        plt.show(block=False)
    
    def stop_animation(self):
        """Stop animation"""
        self.is_running = False
        if self.animation:
            self.animation.event_source.stop()
    
    def clear_data(self):
        """Clear all trajectory data"""
        self.trajectory_data = {
            'time': [],
            'position': [],
            'attitude': []
        }
        
        # Clear queue
        while not self.data_queue.empty():
            try:
                self.data_queue.get_nowait()
            except queue.Empty:
                break
    
    def save_trajectory(self, filename: str):
        """Save trajectory data to file"""
        if not self.trajectory_data['position']:
            print("No trajectory data to save")
            return
        
        data = {
            'time': np.array(self.trajectory_data['time']),
            'position': np.array(self.trajectory_data['position']),
        }
        
        if any(att is not None for att in self.trajectory_data['attitude']):
            attitudes = []
            for att in self.trajectory_data['attitude']:
                if att is not None:
                    attitudes.append(att)
                else:
                    attitudes.append(np.zeros(3))
            data['attitude'] = np.array(attitudes)
        
        np.savez(filename, **data)
        print(f"Trajectory saved to {filename}")
    
    def load_trajectory(self, filename: str):
        """Load and display trajectory from file"""
        try:
            data = np.load(filename)
            
            positions = data['position']
            times = data['time']
            
            # Plot complete trajectory
            self.ax.clear()
            self._setup_plot()
            
            self.trajectory_line, = self.ax.plot(positions[:, 0], positions[:, 1], positions[:, 2],
                                               'b-', linewidth=2, label='Trajectory')
            
            # Set appropriate view limits
            margin = 1.0
            x_min, x_max = positions[:, 0].min() - margin, positions[:, 0].max() + margin
            y_min, y_max = positions[:, 1].min() - margin, positions[:, 1].max() + margin
            z_min, z_max = positions[:, 2].min() - margin, positions[:, 2].max() + margin
            
            self.ax.set_xlim(x_min, x_max)
            self.ax.set_ylim(y_min, y_max)
            self.ax.set_zlim(z_min, z_max)
            
            self.ax.legend()
            plt.show()
            
            print(f"Trajectory loaded from {filename}")
            
        except Exception as e:
            print(f"Error loading trajectory: {e}")
    
    def set_view_limits(self, xlim: Tuple[float, float] = None,
                       ylim: Tuple[float, float] = None,
                       zlim: Tuple[float, float] = None):
        """Set manual view limits"""
        if xlim:
            self.ax.set_xlim(xlim)
        if ylim:
            self.ax.set_ylim(ylim)
        if zlim:
            self.ax.set_zlim(zlim)


class QuadrotorVisualizer:
    """
    Specialized quadrotor visualization with realistic 3D model
    """
    
    def __init__(self, arm_length: float = 0.25):
        """
        Initialize quadrotor visualizer
        
        Args:
            arm_length: Quadrotor arm length (m)
        """
        self.arm_length = arm_length
        self.fig = plt.figure(figsize=(10, 8))
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # Quadrotor geometry
        self.body_lines = []
        self.propeller_circles = []
        
        self._setup_quadrotor_model()
    
    def _setup_quadrotor_model(self):
        """Setup 3D quadrotor model"""
        self.ax.set_xlabel('X (m)')
        self.ax.set_ylabel('Y (m)')
        self.ax.set_zlabel('Z (m)')
        self.ax.set_title('Quadrotor 3D Model')
        
        # Initialize empty quadrotor model
        self._create_quadrotor_frame()
    
    def _create_quadrotor_frame(self):
        """Create quadrotor frame geometry"""
        # Clear existing elements
        for line in self.body_lines:
            line.remove()
        for circle in self.propeller_circles:
            circle.remove()
        
        self.body_lines = []
        self.propeller_circles = []
        
        # Quadrotor arms (X configuration)
        L = self.arm_length / np.sqrt(2)
        
        # Arm positions
        arm_positions = np.array([
            [L, L, 0],    # Motor 1 (front-right)
            [-L, L, 0],   # Motor 2 (front-left) 
            [-L, -L, 0],  # Motor 3 (back-left)
            [L, -L, 0]    # Motor 4 (back-right)
        ])
        
        # Draw arms
        center = np.array([0, 0, 0])
        for pos in arm_positions:
            line, = self.ax.plot([center[0], pos[0]], [center[1], pos[1]], 
                                [center[2], pos[2]], 'k-', linewidth=3)
            self.body_lines.append(line)
        
        # Draw propellers (circles)
        prop_radius = 0.1
        theta = np.linspace(0, 2*np.pi, 20)
        
        for pos in arm_positions:
            x_circle = pos[0] + prop_radius * np.cos(theta)
            y_circle = pos[1] + prop_radius * np.sin(theta)
            z_circle = pos[2] * np.ones_like(theta)
            
            circle, = self.ax.plot(x_circle, y_circle, z_circle, 'r-', linewidth=2)
            self.propeller_circles.append(circle)
        
        # Set equal aspect ratio and limits
        limit = self.arm_length * 1.5
        self.ax.set_xlim(-limit, limit)
        self.ax.set_ylim(-limit, limit)
        self.ax.set_zlim(-limit/2, limit/2)
        self.ax.set_box_aspect([1,1,0.5])
    
    def update_pose(self, position: np.ndarray, attitude: np.ndarray):
        """
        Update quadrotor pose
        
        Args:
            position: Position [x, y, z]
            attitude: Attitude [roll, pitch, yaw]
        """
        # Rotation matrix
        phi, theta, psi = attitude
        R = self._euler_to_rotation_matrix(phi, theta, psi)
        
        # Update frame
        self._create_quadrotor_frame_at_pose(position, R)
    
    def _create_quadrotor_frame_at_pose(self, position: np.ndarray, R: np.ndarray):
        """Create quadrotor frame at specific pose"""
        # Clear existing
        for line in self.body_lines:
            line.remove()
        for circle in self.propeller_circles:
            circle.remove()
        
        self.body_lines = []
        self.propeller_circles = []
        
        # Arm positions in body frame
        L = self.arm_length / np.sqrt(2)
        arm_positions_body = np.array([
            [L, L, 0],
            [-L, L, 0], 
            [-L, -L, 0],
            [L, -L, 0]
        ])
        
        # Transform to world frame
        arm_positions_world = []
        center_world = position
        
        for pos_body in arm_positions_body:
            pos_world = center_world + R @ pos_body
            arm_positions_world.append(pos_world)
            
            # Draw arm
            line, = self.ax.plot([center_world[0], pos_world[0]], 
                                [center_world[1], pos_world[1]],
                                [center_world[2], pos_world[2]], 'k-', linewidth=3)
            self.body_lines.append(line)
        
        # Draw propellers
        prop_radius = 0.1
        theta = np.linspace(0, 2*np.pi, 20)
        
        for pos_world in arm_positions_world:
            # Propeller in body frame (horizontal circle)
            prop_body = np.array([prop_radius * np.cos(theta),
                                 prop_radius * np.sin(theta),
                                 np.zeros_like(theta)]).T
            
            # Transform to world frame
            prop_world = np.array([pos_world + R @ p for p in prop_body])
            
            circle, = self.ax.plot(prop_world[:, 0], prop_world[:, 1], prop_world[:, 2], 
                                  'r-', linewidth=2)
            self.propeller_circles.append(circle)
        
        plt.draw()
    
    def _euler_to_rotation_matrix(self, phi: float, theta: float, psi: float) -> np.ndarray:
        """Convert Euler angles to rotation matrix"""
        cos_phi, sin_phi = np.cos(phi), np.sin(phi)
        cos_theta, sin_theta = np.cos(theta), np.sin(theta)
        cos_psi, sin_psi = np.cos(psi), np.sin(psi)
        
        R = np.array([
            [cos_theta * cos_psi, 
             sin_phi * sin_theta * cos_psi - cos_phi * sin_psi,
             cos_phi * sin_theta * cos_psi + sin_phi * sin_psi],
            
            [cos_theta * sin_psi,
             sin_phi * sin_theta * sin_psi + cos_phi * cos_psi,
             cos_phi * sin_theta * sin_psi - sin_phi * cos_psi],
            
            [-sin_theta,
             sin_phi * cos_theta,
             cos_phi * cos_theta]
        ])
        
        return R
    
    def show(self):
        """Show the visualization"""
        plt.show()