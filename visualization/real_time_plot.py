"""
Real-time plotting and visualization
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
from mpl_toolkits.mplot3d import Axes3D
from typing import Dict, List, Optional, Callable, Any
import threading
import queue
import time


class RealTimePlotter:
    """
    Real-time data plotting with multiple subplots
    """
    
    def __init__(self, plot_config: Dict[str, Any], update_interval: int = 50):
        """
        Initialize real-time plotter
        
        Args:
            plot_config: Dictionary defining plots and their configuration
            update_interval: Update interval in milliseconds
        """
        self.plot_config = plot_config
        self.update_interval = update_interval
        
        # Data storage
        self.data_buffer = {}
        self.max_points = 1000  # Maximum points to keep in buffer
        
        # Plotting setup
        self.fig = None
        self.axes = {}
        self.lines = {}
        self.animation = None
        
        # Thread safety
        self.data_queue = queue.Queue()
        self.lock = threading.Lock()
        
        # Status
        self.is_running = False
        
        self._setup_plots()
    
    def _setup_plots(self):
        """Setup matplotlib figure and subplots"""
        num_plots = len(self.plot_config)
        
        if num_plots == 1:
            self.fig, ax = plt.subplots(figsize=(10, 6))
            self.axes = {list(self.plot_config.keys())[0]: ax}
        else:
            # Calculate subplot arrangement
            cols = min(2, num_plots)
            rows = (num_plots + cols - 1) // cols
            
            self.fig, axes = plt.subplots(rows, cols, figsize=(12, 4*rows))
            if num_plots == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes.flatten()
            else:
                axes = axes.flatten()
            
            # Map plot names to axes
            for i, plot_name in enumerate(self.plot_config.keys()):
                if i < len(axes):
                    self.axes[plot_name] = axes[i]
        
        # Initialize data buffers and lines
        for plot_name, config in self.plot_config.items():
            if plot_name not in self.axes:
                continue
                
            ax = self.axes[plot_name]
            
            # Setup axis
            ax.set_title(config.get('title', plot_name))
            ax.set_xlabel(config.get('xlabel', 'Time (s)'))
            ax.set_ylabel(config.get('ylabel', 'Value'))
            ax.grid(True, alpha=0.3)
            
            # Initialize data buffer
            self.data_buffer[plot_name] = {
                'time': [],
                'data': {}
            }
            
            # Create line objects
            self.lines[plot_name] = {}
            for series_name in config.get('series', ['data']):
                line, = ax.plot([], [], label=series_name, 
                               linewidth=config.get('linewidth', 1.0))
                self.lines[plot_name][series_name] = line
                self.data_buffer[plot_name]['data'][series_name] = []
            
            # Setup legend if multiple series
            if len(config.get('series', ['data'])) > 1:
                ax.legend()
            
            # Set axis limits if specified
            if 'xlim' in config:
                ax.set_xlim(config['xlim'])
            if 'ylim' in config:
                ax.set_ylim(config['ylim'])
        
        plt.tight_layout()
    
    def add_data_point(self, plot_name: str, time_val: float, 
                      data: Dict[str, float]):
        """
        Add data point to plot
        
        Args:
            plot_name: Name of the plot
            time_val: Time value
            data: Dictionary of series_name -> value
        """
        try:
            self.data_queue.put((plot_name, time_val, data), block=False)
        except queue.Full:
            # If queue is full, remove oldest item
            try:
                self.data_queue.get_nowait()
                self.data_queue.put((plot_name, time_val, data), block=False)
            except queue.Empty:
                pass
    
    def _update_plots(self, frame):
        """Update plots with new data (called by animation)"""
        # Process all queued data
        while True:
            try:
                plot_name, time_val, data = self.data_queue.get_nowait()
                
                if plot_name in self.data_buffer:
                    # Add to buffer
                    buffer = self.data_buffer[plot_name]
                    buffer['time'].append(time_val)
                    
                    for series_name, value in data.items():
                        if series_name in buffer['data']:
                            buffer['data'][series_name].append(value)
                    
                    # Limit buffer size
                    if len(buffer['time']) > self.max_points:
                        buffer['time'] = buffer['time'][-self.max_points:]
                        for series_name in buffer['data']:
                            buffer['data'][series_name] = buffer['data'][series_name][-self.max_points:]
                
            except queue.Empty:
                break
        
        # Update line data
        for plot_name, buffer in self.data_buffer.items():
            if plot_name not in self.lines or not buffer['time']:
                continue
            
            ax = self.axes[plot_name]
            time_data = np.array(buffer['time'])
            
            for series_name, line in self.lines[plot_name].items():
                if series_name in buffer['data'] and buffer['data'][series_name]:
                    y_data = np.array(buffer['data'][series_name])
                    line.set_data(time_data, y_data)
            
            # Auto-scale if not fixed
            config = self.plot_config[plot_name]
            if 'xlim' not in config and len(time_data) > 0:
                margin = (time_data[-1] - time_data[0]) * 0.1
                ax.set_xlim(time_data[0] - margin, time_data[-1] + margin)
            
            if 'ylim' not in config:
                ax.relim()
                ax.autoscale_view()
        
        return []
    
    def start(self):
        """Start real-time plotting"""
        if self.is_running:
            return
        
        self.is_running = True
        self.animation = FuncAnimation(
            self.fig, self._update_plots, interval=self.update_interval,
            blit=False, cache_frame_data=False
        )
        plt.show(block=False)
    
    def stop(self):
        """Stop real-time plotting"""
        self.is_running = False
        if self.animation:
            self.animation.event_source.stop()
    
    def clear_data(self):
        """Clear all plot data"""
        for plot_name in self.data_buffer:
            self.data_buffer[plot_name]['time'].clear()
            for series_name in self.data_buffer[plot_name]['data']:
                self.data_buffer[plot_name]['data'][series_name].clear()
    
    def save_figure(self, filename: str, dpi: int = 300):
        """Save current figure"""
        self.fig.savefig(filename, dpi=dpi, bbox_inches='tight')


class UAVStateMonitor:
    """
    Specialized monitor for UAV state visualization
    """
    
    def __init__(self, update_interval: int = 50):
        """Initialize UAV state monitor"""
        
        # Define plot configuration for UAV states
        plot_config = {
            'position': {
                'title': 'Position',
                'xlabel': 'Time (s)',
                'ylabel': 'Position (m)',
                'series': ['x', 'y', 'z']
            },
            'velocity': {
                'title': 'Velocity',
                'xlabel': 'Time (s)', 
                'ylabel': 'Velocity (m/s)',
                'series': ['vx', 'vy', 'vz']
            },
            'attitude': {
                'title': 'Attitude',
                'xlabel': 'Time (s)',
                'ylabel': 'Angle (deg)',
                'series': ['roll', 'pitch', 'yaw']
            },
            'angular_velocity': {
                'title': 'Angular Velocity',
                'xlabel': 'Time (s)',
                'ylabel': 'Angular Velocity (deg/s)',
                'series': ['p', 'q', 'r']
            }
        }
        
        self.plotter = RealTimePlotter(plot_config, update_interval)
        self.last_update_time = 0
        self.update_rate = 10  # Hz
    
    def update_state(self, current_time: float, state, control_inputs=None):
        """
        Update plots with current UAV state
        
        Args:
            current_time: Current simulation time
            state: UAV state object
            control_inputs: Optional control inputs
        """
        # Throttle updates
        if current_time - self.last_update_time < 1.0 / self.update_rate:
            return
        
        self.last_update_time = current_time
        
        # Position data
        self.plotter.add_data_point('position', current_time, {
            'x': state.position[0],
            'y': state.position[1], 
            'z': state.position[2]
        })
        
        # Velocity data
        self.plotter.add_data_point('velocity', current_time, {
            'vx': state.velocity[0],
            'vy': state.velocity[1],
            'vz': state.velocity[2]
        })
        
        # Attitude data (convert to degrees)
        self.plotter.add_data_point('attitude', current_time, {
            'roll': np.degrees(state.attitude[0]),
            'pitch': np.degrees(state.attitude[1]),
            'yaw': np.degrees(state.attitude[2])
        })
        
        # Angular velocity data (convert to degrees)
        self.plotter.add_data_point('angular_velocity', current_time, {
            'p': np.degrees(state.angular_velocity[0]),
            'q': np.degrees(state.angular_velocity[1]),
            'r': np.degrees(state.angular_velocity[2])
        })
    
    def start(self):
        """Start monitoring"""
        self.plotter.start()
    
    def stop(self):
        """Stop monitoring"""
        self.plotter.stop()
    
    def clear(self):
        """Clear all data"""
        self.plotter.clear_data()
    
    def save_plots(self, filename: str):
        """Save current plots"""
        self.plotter.save_figure(filename)


class ControlMonitor:
    """
    Monitor for control signals and errors
    """
    
    def __init__(self, update_interval: int = 50):
        """Initialize control monitor"""
        
        plot_config = {
            'control_inputs': {
                'title': 'Motor Commands',
                'xlabel': 'Time (s)',
                'ylabel': 'Motor Speed (rad/s)', 
                'series': ['motor1', 'motor2', 'motor3', 'motor4']
            },
            'attitude_errors': {
                'title': 'Attitude Errors',
                'xlabel': 'Time (s)',
                'ylabel': 'Error (deg)',
                'series': ['roll_error', 'pitch_error', 'yaw_error']
            },
            'control_moments': {
                'title': 'Control Moments',
                'xlabel': 'Time (s)',
                'ylabel': 'Moment (N*m)',
                'series': ['Mx', 'My', 'Mz']
            }
        }
        
        self.plotter = RealTimePlotter(plot_config, update_interval)
        self.last_update_time = 0
        self.update_rate = 10  # Hz
    
    def update_control(self, current_time: float, motor_speeds: np.ndarray,
                      control_moments: np.ndarray = None, errors: Dict = None):
        """
        Update control monitoring plots
        
        Args:
            current_time: Current time
            motor_speeds: Motor speed commands
            control_moments: Control moments [Mx, My, Mz]
            errors: Dictionary of control errors
        """
        # Throttle updates
        if current_time - self.last_update_time < 1.0 / self.update_rate:
            return
            
        self.last_update_time = current_time
        
        # Motor commands
        if len(motor_speeds) >= 4:
            self.plotter.add_data_point('control_inputs', current_time, {
                'motor1': motor_speeds[0],
                'motor2': motor_speeds[1],
                'motor3': motor_speeds[2],
                'motor4': motor_speeds[3]
            })
        
        # Control moments
        if control_moments is not None and len(control_moments) >= 3:
            self.plotter.add_data_point('control_moments', current_time, {
                'Mx': control_moments[0],
                'My': control_moments[1],
                'Mz': control_moments[2]
            })
        
        # Attitude errors
        if errors:
            error_data = {}
            for key, value in errors.items():
                if 'error' in key:
                    # Convert to degrees if it's an angle error
                    if any(angle in key for angle in ['roll', 'pitch', 'yaw']):
                        error_data[key] = np.degrees(value)
                    else:
                        error_data[key] = value
            
            if error_data:
                self.plotter.add_data_point('attitude_errors', current_time, error_data)
    
    def start(self):
        """Start monitoring"""
        self.plotter.start()
    
    def stop(self):
        """Stop monitoring"""
        self.plotter.stop()
    
    def clear(self):
        """Clear all data"""
        self.plotter.clear_data()