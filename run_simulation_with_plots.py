#!/usr/bin/env python3
"""
Run simulation and automatically generate visualization plots
"""

import subprocess
import sys
import os

def main():
    """Run simulation and generate plots"""
    
    print("🚁 Starting UAV Simulation with Visualization...")
    print("=" * 60)
    
    # Step 1: Run the simulation
    print("\n📊 Step 1: Running simulation...")
    result = subprocess.run([sys.executable, "examples/basic_hover_simulation.py"], 
                          capture_output=False)
    
    if result.returncode != 0:
        print("❌ Simulation failed!")
        return 1
    
    # Step 2: Generate visualization
    print("\n🎨 Step 2: Generating visualization...")
    result = subprocess.run([sys.executable, "visualize_results.py"], 
                          capture_output=False)
    
    if result.returncode != 0:
        print("❌ Visualization failed!")
        return 1
    
    # Step 3: Show results
    print("\n✅ Complete! Generated files:")
    print("   📈 uav_simulation_results.png - Visualization plots")
    print("   📊 hover_simulation_report.txt - Analysis report")
    print("   💾 simulation_data/ - Raw data files")
    
    # Check if files exist
    if os.path.exists("uav_simulation_results.png"):
        print("\n🎯 Visualization image ready: uav_simulation_results.png")
    else:
        print("\n⚠️  Warning: Visualization image not found")
    
    return 0

if __name__ == "__main__":
    exit(main())