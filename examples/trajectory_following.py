#!/usr/bin/env python3
"""
Trajectory following simulation example

This example demonstrates:
1. Complex trajectory generation
2. Position and attitude control
3. Multi-level control architecture
4. Advanced visualization
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.simulation import Simulation
from core.component import DynamicsComponent, ControllerComponent
from core.state import UAVState
from dynamics.quadrotor import QuadrotorDynamics
from dynamics.propulsion import PropulsionSystem
from control.pid_controller import AttitudePIDController, VectorPIDController
from control.base_controller import AttitudeReference, PositionReference, TrajectoryReference
from visualization.real_time_plot import UAVStateMonitor, ControlMonitor
from visualization.trajectory_viewer import TrajectoryViewer3D
from data.recorder import DataRecorder
from data.analyzer import DataAnalyzer


class PositionController:
    """
    Position controller that outputs attitude references
    """
    
    def __init__(self, mass: float = 1.5, gravity: float = 9.81):
        self.mass = mass
        self.gravity = gravity
        
        # Position PID controllers
        self.x_pid = VectorPIDController("position_xy", 2, kp=2.0, ki=0.1, kd=1.0)
        self.z_pid = VectorPIDController("position_z", 1, kp=3.0, ki=0.2, kd=1.5)
        
        # Yaw controller
        self.yaw_pid = VectorPIDController("yaw", 1, kp=2.0, ki=0.05, kd=0.1)
    
    def compute_control(self, reference: PositionReference, current_state: UAVState, dt: float) -> AttitudeReference:
        """
        Compute attitude reference from position reference
        
        Args:
            reference: Position reference
            current_state: Current UAV state
            dt: Time step
            
        Returns:
            Attitude reference
        """
        # Position control (XY)
        pos_error_xy = reference.position[:2] - current_state.position[:2]
        vel_error_xy = reference.velocity[:2] - current_state.velocity[:2]
        
        # Compute desired accelerations
        accel_xy = self.x_pid.compute_control(pos_error_xy, np.zeros(2), dt) + vel_error_xy
        
        # Convert to attitude references (assuming small angles)
        # ax = g * tan(theta) ≈ g * theta for small theta
        # ay = -g * tan(phi) ≈ -g * phi for small phi
        desired_pitch = np.arctan2(accel_xy[0], self.gravity)
        desired_roll = -np.arctan2(accel_xy[1], self.gravity)
        
        # Limit attitude commands
        max_angle = np.radians(30)  # 30 degrees max
        desired_roll = np.clip(desired_roll, -max_angle, max_angle)
        desired_pitch = np.clip(desired_pitch, -max_angle, max_angle)
        
        # Altitude control
        alt_error = reference.position[2] - current_state.position[2]
        vel_error_z = reference.velocity[2] - current_state.velocity[2]
        
        thrust_accel = self.z_pid.compute_control(np.array([alt_error]), np.array([0]), dt)[0]
        thrust_accel += vel_error_z + self.gravity  # Compensate gravity
        thrust = self.mass * thrust_accel
        
        # Yaw control
        yaw_error = reference.yaw - current_state.attitude[2]
        # Wrap yaw error to [-pi, pi]
        yaw_error = np.arctan2(np.sin(yaw_error), np.cos(yaw_error))
        desired_yaw = self.yaw_pid.compute_control(np.array([yaw_error]), np.array([0]), dt)[0]
        
        return AttitudeReference(
            roll=desired_roll,
            pitch=desired_pitch,
            yaw=desired_yaw,
            thrust=thrust
        )
    
    def reset(self):
        """Reset all PID controllers"""
        self.x_pid.reset()
        self.z_pid.reset()
        self.yaw_pid.reset()


def generate_circle_trajectory(radius: float = 2.0, height: float = -2.0, 
                             duration: float = 20.0, dt: float = 0.1) -> TrajectoryReference:
    """
    Generate circular trajectory
    
    Args:
        radius: Circle radius (m)
        height: Flight height (m, negative for above ground)
        duration: Total duration (s)
        dt: Time step (s)
        
    Returns:
        Trajectory reference object
    """
    trajectory = TrajectoryReference()
    
    times = np.arange(0, duration + dt, dt)
    
    for t in times:
        # Circular motion
        omega = 2 * np.pi / duration  # One full circle
        
        x = radius * np.cos(omega * t)
        y = radius * np.sin(omega * t)
        z = height
        
        # Velocities (tangent to circle)
        vx = -radius * omega * np.sin(omega * t)
        vy = radius * omega * np.cos(omega * t)
        vz = 0.0
        
        # Yaw to face motion direction
        yaw = np.arctan2(vy, vx)
        
        trajectory.add_waypoint(
            time=t,
            position=np.array([x, y, z]),
            velocity=np.array([vx, vy, vz]),
            yaw=yaw
        )
    
    return trajectory


def generate_figure8_trajectory(size: float = 2.0, height: float = -2.0,
                               duration: float = 24.0, dt: float = 0.1) -> TrajectoryReference:
    """
    Generate figure-8 trajectory
    
    Args:
        size: Figure-8 size (m)
        height: Flight height (m)
        duration: Total duration (s)
        dt: Time step (s)
        
    Returns:
        Trajectory reference object
    """
    trajectory = TrajectoryReference()
    
    times = np.arange(0, duration + dt, dt)
    
    for t in times:
        # Figure-8 parametric equations
        omega = 2 * np.pi / duration
        
        x = size * np.sin(omega * t)
        y = size * np.sin(omega * t) * np.cos(omega * t)
        z = height
        
        # Velocities
        vx = size * omega * np.cos(omega * t)
        vy = size * omega * (np.cos(omega * t)**2 - np.sin(omega * t)**2)
        vz = 0.0
        
        # Yaw to face motion direction
        yaw = np.arctan2(vy, vx)
        
        trajectory.add_waypoint(
            time=t,
            position=np.array([x, y, z]),
            velocity=np.array([vx, vy, vz]),
            yaw=yaw
        )
    
    return trajectory


def main():
    """Main simulation function"""
    
    print("Starting UAV Trajectory Following Simulation...")
    print("=" * 60)
    
    # Simulation parameters
    dt = 0.01  # 100 Hz simulation
    sim_duration = 25.0  # seconds
    
    # Create simulation
    sim = Simulation(dt=dt, real_time=False)
    
    # 1. Create UAV dynamics model
    uav_mass = 1.5  # kg
    uav_inertia = np.diag([0.029, 0.029, 0.055])  # kg*m^2
    
    dynamics_model = QuadrotorDynamics(
        mass=uav_mass,
        inertia=uav_inertia,
        gravity=9.81,
        drag_coefficient=0.02
    )
    
    # Initial state
    initial_state = UAVState(
        position=np.array([2.0, 0.0, -2.0]),  # Start on circle
        velocity=np.zeros(3),
        attitude=np.zeros(3),
        angular_velocity=np.zeros(3),
        time=0.0
    )
    
    # Create dynamics component
    dynamics_comp = DynamicsComponent("uav_dynamics", dynamics_model)
    dynamics_comp.initialize(initial_state=initial_state)
    
    # 2. Create propulsion system
    propulsion = PropulsionSystem(num_motors=4)
    
    # 3. Create control system (cascaded)
    # Position controller (outer loop)
    position_controller = PositionController(mass=uav_mass, gravity=9.81)
    
    # Attitude controller (inner loop)
    attitude_controller = AttitudePIDController(
        name="attitude_pid",
        mass=uav_mass,
        gravity=9.81
    )
    
    # Tune controllers
    attitude_controller.set_attitude_gains('roll', kp=8.0, ki=0.3, kd=0.4)
    attitude_controller.set_attitude_gains('pitch', kp=8.0, ki=0.3, kd=0.4)
    attitude_controller.set_attitude_gains('yaw', kp=5.0, ki=0.2, kd=0.3)
    
    # 4. Generate trajectory
    print("Generating trajectory...")
    trajectory_type = "figure8"  # "circle" or "figure8"
    
    if trajectory_type == "circle":
        trajectory = generate_circle_trajectory(radius=2.0, height=-2.0, duration=20.0)
        print("Generated circular trajectory (radius=2m, duration=20s)")
    else:
        trajectory = generate_figure8_trajectory(size=2.0, height=-2.0, duration=24.0)
        print("Generated figure-8 trajectory (size=2m, duration=24s)")
    
    # 5. Set up data recording
    recorder = DataRecorder(max_buffer_size=int(sim_duration/dt) + 100)
    recorder.set_metadata(
        simulation_type="trajectory_following",
        trajectory_type=trajectory_type,
        duration=sim_duration,
        dt=dt,
        vehicle_mass=uav_mass
    )
    sim.set_data_recorder(recorder)
    
    # 6. Set up visualization
    state_monitor = UAVStateMonitor(update_interval=100)
    control_monitor = ControlMonitor(update_interval=100)
    trajectory_viewer = TrajectoryViewer3D()
    
    # Set reference trajectory for visualization
    ref_positions = []
    for t in np.arange(0, sim_duration, 0.1):
        ref = trajectory.get_reference(t)
        ref_positions.append(ref.position)
    
    if ref_positions:
        trajectory_viewer.set_reference_trajectory(np.array(ref_positions))
    
    # Start visualizations
    print("Starting real-time visualization...")
    state_monitor.start()
    control_monitor.start()
    trajectory_viewer.start_animation(interval=50)
    
    # 7. Simulation variables
    position_errors = []
    attitude_errors = []
    times = []
    
    # 8. Simulation loop
    print(f"\nRunning simulation for {sim_duration} seconds...")
    print("Following", trajectory_type, "trajectory")
    
    recorder.start_recording(f"trajectory_{trajectory_type}")
    
    try:
        step_count = 0
        while sim.current_time < sim_duration:
            current_time = sim.current_time
            
            # Get trajectory reference
            pos_ref = trajectory.get_reference(current_time)
            current_state = dynamics_comp.get_output('state')
            
            if current_state is None:
                break
            
            # Position control (outer loop)
            att_ref = position_controller.compute_control(pos_ref, current_state, dt)
            
            # Attitude control (inner loop)
            control_moments_thrust = attitude_controller.compute_control(att_ref, current_state, dt)
            
            # Motor allocation
            motor_speeds = propulsion.allocate_control(control_moments_thrust)
            motor_speeds_actual = propulsion.update_motors(dt, motor_speeds)
            
            # Update dynamics
            dynamics_comp.set_input('control_inputs', motor_speeds_actual)
            dynamics_comp.update_inputs()
            dynamics_results = dynamics_comp.update(dt)
            
            # Update visualization
            if step_count % 5 == 0:  # Update every 5 steps to reduce overhead
                state_monitor.update_state(current_time, current_state)
                trajectory_viewer.add_state_data(current_time, current_state.position, current_state.attitude)
                
                control_monitor.update_control(
                    current_time,
                    motor_speeds_actual,
                    control_moments_thrust[:3],
                    attitude_controller.get_attitude_errors()
                )
            
            # Record errors for analysis
            pos_error = np.linalg.norm(current_state.position - pos_ref.position)
            att_error = np.linalg.norm(current_state.attitude - np.array([att_ref.roll, att_ref.pitch, att_ref.yaw]))
            
            position_errors.append(pos_error)
            attitude_errors.append(att_error)
            times.append(current_time)
            
            # Print status
            if step_count % 100 == 0:
                print(f"Time: {current_time:.1f}s - Pos Error: {pos_error:.3f}m - Att Error: {np.degrees(att_error):.1f}°")
            
            # Advance simulation
            sim.current_time += dt
            sim.step_count += 1
            step_count += 1
            
            # Simulate data recording
            results = {
                'uav_dynamics': dynamics_results,
                'position_error': pos_error,
                'attitude_error': att_error,
                'reference_position': pos_ref.position.tolist(),
                'motor_speeds': motor_speeds_actual.tolist()
            }
            
            for callback in sim.step_callbacks:
                callback(sim, results)
        
        print(f"\nSimulation completed successfully!")
        print(f"Total steps: {step_count}")
        
    except KeyboardInterrupt:
        print("\nSimulation interrupted by user")
    
    finally:
        # Stop recording and visualization
        session_name = recorder.stop_recording()
        state_monitor.stop()
        control_monitor.stop()
        trajectory_viewer.stop_animation()
        
        # Analysis
        print("\n" + "="*60)
        print("TRAJECTORY FOLLOWING ANALYSIS")
        print("="*60)
        
        if position_errors and attitude_errors:
            pos_errors = np.array(position_errors)
            att_errors = np.array(attitude_errors)
            
            print(f"Position Tracking:")
            print(f"  - Mean Error: {np.mean(pos_errors):.3f} m")
            print(f"  - Max Error: {np.max(pos_errors):.3f} m")
            print(f"  - RMS Error: {np.sqrt(np.mean(pos_errors**2)):.3f} m")
            
            print(f"\nAttitude Tracking:")
            print(f"  - Mean Error: {np.degrees(np.mean(att_errors)):.1f} deg")
            print(f"  - Max Error: {np.degrees(np.max(att_errors)):.1f} deg")
            print(f"  - RMS Error: {np.degrees(np.sqrt(np.mean(att_errors**2))):.1f} deg")
            
            # Plot error analysis
            plt.figure(figsize=(12, 8))
            
            plt.subplot(2, 2, 1)
            plt.plot(times, pos_errors)
            plt.title('Position Tracking Error')
            plt.xlabel('Time (s)')
            plt.ylabel('Error (m)')
            plt.grid(True, alpha=0.3)
            
            plt.subplot(2, 2, 2)
            plt.plot(times, np.degrees(att_errors))
            plt.title('Attitude Tracking Error')
            plt.xlabel('Time (s)')
            plt.ylabel('Error (deg)')
            plt.grid(True, alpha=0.3)
            
            plt.subplot(2, 2, 3)
            plt.hist(pos_errors, bins=30, alpha=0.7, edgecolor='black')
            plt.title('Position Error Distribution')
            plt.xlabel('Error (m)')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
            
            plt.subplot(2, 2, 4)
            plt.hist(np.degrees(att_errors), bins=30, alpha=0.7, edgecolor='black')
            plt.title('Attitude Error Distribution')
            plt.xlabel('Error (deg)')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('trajectory_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()
        
        # Save data
        print("\nSaving simulation data...")
        hdf5_file = recorder.save_data(format='hdf5')
        json_file = recorder.save_data(format='json')
        
        print(f"\nData files saved:")
        print(f"  - HDF5: {hdf5_file}")
        print(f"  - JSON: {json_file}")
        print(f"  - Analysis Plot: trajectory_analysis.png")
        
        print("\nTrajectory following simulation complete!")


if __name__ == "__main__":
    main()