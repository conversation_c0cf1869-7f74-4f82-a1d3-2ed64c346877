# UAV Simulation Examples

This directory contains example scripts demonstrating various features of the UAV simulation system.

## Examples

### 1. Basic Hover Simulation (`basic_hover_simulation.py`)

**Purpose**: Demonstrates basic simulation setup and attitude control

**Features**:
- Six degree-of-freedom UAV dynamics
- PID attitude controller
- Real-time state monitoring
- Data recording and analysis
- Performance metrics computation

**Usage**:
```bash
cd examples
python basic_hover_simulation.py
```

**What it does**:
- Starts UAV with small initial attitude disturbances
- Attempts to hover at 1m altitude with level attitude
- Shows real-time plots of position, velocity, attitude
- Records simulation data for later analysis
- Generates performance report

**Expected Results**:
- UAV should stabilize to hover position within ~2-3 seconds
- Position error should be < 0.1m
- Attitude error should be < 5 degrees

### 2. Trajectory Following (`trajectory_following.py`)

**Purpose**: Demonstrates advanced trajectory following with cascaded control

**Features**:
- Position and attitude control (cascaded architecture)
- Complex trajectory generation (circle, figure-8)
- 3D trajectory visualization
- Advanced performance analysis
- Real-time tracking error monitoring

**Usage**:
```bash
cd examples
python trajectory_following.py
```

**What it does**:
- Generates a complex trajectory (circle or figure-8)
- Uses outer-loop position controller to generate attitude references
- Uses inner-loop attitude controller for stabilization
- Shows 3D visualization with reference trajectory
- Analyzes tracking performance

**Expected Results**:
- UAV should follow the reference trajectory closely
- Position tracking error should be < 0.2m
- Smooth trajectory following without oscillations

## Running the Examples

### Prerequisites

Make sure you have the required dependencies installed:

```bash
pip install numpy scipy matplotlib h5py pandas
```

### Basic Usage

1. Navigate to the project root directory
2. Run any example:
   ```bash
   python examples/basic_hover_simulation.py
   ```

### Customization

You can modify the examples to test different scenarios:

#### Hover Simulation
- Change initial conditions in `initial_state`
- Adjust controller gains in `set_attitude_gains()`
- Modify UAV parameters (mass, inertia)
- Change simulation duration

#### Trajectory Following
- Switch between "circle" and "figure8" trajectories
- Adjust trajectory parameters (size, duration)
- Tune position and attitude controller gains
- Add custom trajectory types

## Understanding the Output

### Real-time Visualization

The examples show multiple real-time plots:

1. **State Monitor**: 
   - Position (x, y, z) vs time
   - Velocity components vs time
   - Attitude angles (roll, pitch, yaw) vs time
   - Angular velocities vs time

2. **Control Monitor**:
   - Motor speed commands
   - Control moments (Mx, My, Mz)
   - Attitude tracking errors

3. **3D Trajectory Viewer**:
   - 3D flight path
   - Current UAV position and orientation
   - Reference trajectory (if available)

### Data Recording

All examples automatically record:
- Complete state history
- Control inputs and outputs
- Performance metrics
- Simulation metadata

Data is saved in multiple formats:
- **HDF5**: Efficient binary format for analysis
- **JSON**: Human-readable format for inspection

### Performance Analysis

The examples generate:
- Statistical performance metrics
- Frequency domain analysis
- Tracking error analysis
- Comprehensive performance reports

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're running from the correct directory
2. **Visualization Issues**: Some systems may have matplotlib backend issues
3. **Performance**: Real-time visualization may slow down on older computers

### Performance Tips

- Set `real_time=False` in Simulation for faster execution
- Reduce visualization update rates for better performance
- Increase `dt` for faster simulation (with reduced accuracy)

### Debugging

- Enable debug prints by modifying the callback functions
- Check simulation statistics with `sim.get_statistics()`
- Examine recorded data for detailed analysis

## Creating Custom Examples

To create your own simulation:

1. Copy one of the existing examples
2. Modify the UAV parameters and initial conditions
3. Implement your control algorithm
4. Add custom visualization or analysis
5. Test and tune your system

See the main documentation for detailed API reference.