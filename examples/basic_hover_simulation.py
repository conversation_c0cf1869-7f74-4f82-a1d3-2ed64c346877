#!/usr/bin/env python3
"""
Basic hover simulation example

This example demonstrates:
1. Setting up a complete UAV simulation
2. Implementing attitude control
3. Real-time visualization
4. Data recording and analysis
"""

import sys
import os
import numpy as np

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.simulation import Simulation
from core.component import DynamicsComponent, ControllerComponent
from core.state import UAVState
from dynamics.quadrotor import QuadrotorDynamics
from dynamics.propulsion import PropulsionSystem
from control.pid_controller import AttitudePIDController
from control.base_controller import AttitudeReference
# from visualization.real_time_plot import UAVStateMonitor, ControlMonitor
# from visualization.trajectory_viewer import TrajectoryViewer3D
from data.recorder import DataRecorder
from data.analyzer import DataAnalyzer


def main():
    """Main simulation function"""
    
    print("Starting UAV Hover Simulation...")
    print("=" * 50)
    
    print("DEBUG: Imports completed successfully")
    
    # Simulation parameters
    dt = 0.01  # 100 Hz simulation
    sim_duration = 2.0  # seconds (reduced for testing)
    
    # Create simulation
    print("DEBUG: Creating simulation...")
    sim = Simulation(dt=dt, real_time=False)
    print("DEBUG: Simulation created")
    
    # 1. Create UAV dynamics model
    uav_mass = 1.5  # kg
    uav_inertia = np.diag([0.029, 0.029, 0.055])  # kg*m^2
    
    dynamics_model = QuadrotorDynamics(
        mass=uav_mass,
        inertia=uav_inertia,
        gravity=9.81,
        drag_coefficient=0.01
    )
    
    # Initial state (slightly off from hover to see control action)
    initial_state = UAVState(
        position=np.array([0.0, 0.0, -1.0]),  # 1m above ground
        velocity=np.zeros(3),
        attitude=np.array([np.radians(5), np.radians(-3), np.radians(10)]),  # Small initial tilts
        angular_velocity=np.zeros(3),
        time=0.0
    )
    
    # Create dynamics component
    dynamics_comp = DynamicsComponent("uav_dynamics", dynamics_model)
    dynamics_comp.initialize(initial_state=initial_state)
    print(f"DEBUG: Dynamics component initialized, state is: {dynamics_comp.state is not None}")
    
    # 2. Create propulsion system
    propulsion = PropulsionSystem(num_motors=4)
    
    # 3. Create attitude controller
    attitude_controller = AttitudePIDController(
        name="attitude_pid",
        mass=uav_mass,
        gravity=9.81
    )
    
    # Tune controller gains for better performance (conservative for stability)
    attitude_controller.set_attitude_gains('roll', kp=2.0, ki=0.05, kd=0.1)
    attitude_controller.set_attitude_gains('pitch', kp=2.0, ki=0.05, kd=0.1)
    attitude_controller.set_attitude_gains('yaw', kp=1.0, ki=0.02, kd=0.05)
    
    controller_comp = ControllerComponent("attitude_controller", attitude_controller)
    controller_comp.initialize()
    
    # 4. Add components to simulation (simplified approach)
    # sim.add_component(dynamics_comp, update_priority=1)
    # sim.add_component(controller_comp, update_priority=0)
    
    # 5. Set up data recording
    recorder = DataRecorder(max_buffer_size=int(sim_duration/dt) + 100)
    recorder.set_metadata(
        simulation_type="hover_test",
        duration=sim_duration,
        dt=dt,
        vehicle_mass=uav_mass,
        controller_type="attitude_pid"
    )
    sim.set_data_recorder(recorder)
    
    # 6. Set up visualization (disabled due to matplotlib issues)
    # state_monitor = UAVStateMonitor(update_interval=100)
    # control_monitor = ControlMonitor(update_interval=100)
    # trajectory_viewer = TrajectoryViewer3D()
    
    # Start visualizations
    print("Real-time visualization disabled (matplotlib compatibility issues)")
    # state_monitor.start()
    # control_monitor.start()
    # trajectory_viewer.start_animation(interval=100)
    
    # 7. Define simulation callbacks
    def simulation_step_callback(sim_instance, results):
        """Called after each simulation step"""
        current_time = sim_instance.current_time
        
        # Get current state
        if 'uav_dynamics' in results:
            state = results['uav_dynamics'].get('state')
            if state:
                # Update visualizations (disabled)
                # state_monitor.update_state(current_time, state)
                # trajectory_viewer.add_state_data(current_time, state.position, state.attitude)
                pass
        
        # Get control data
        if 'attitude_controller' in results:
            control_output = results['attitude_controller'].get('control_output')
            if control_output is not None:
                # Convert control moments to motor speeds using propulsion system
                motor_speeds = propulsion.allocate_control(control_output)
                
                # control_monitor.update_control(
                #     current_time, 
                #     motor_speeds,
                #     control_output[:3],  # Control moments
                #     attitude_controller.get_attitude_errors()
                # )
        
        # Print status every second
        if abs(current_time - round(current_time)) < dt/2:
            if 'uav_dynamics' in results and results['uav_dynamics'].get('state'):
                state = results['uav_dynamics']['state']
                print(f"Time: {current_time:.1f}s - Position: [{state.position[0]:.3f}, {state.position[1]:.3f}, {state.position[2]:.3f}]")
    
    # Add callback
    sim.add_callback('step', simulation_step_callback)
    
    # 8. Create reference signal (hover at 1m altitude)
    hover_reference = AttitudeReference(
        roll=0.0,
        pitch=0.0, 
        yaw=0.0,
        thrust=uav_mass * 9.81  # Hover thrust
    )
    
    # 9. Simplified simulation loop
    print(f"\nRunning simulation for {sim_duration} seconds...")
    print("Target: Hover at (0, 0, -1) with level attitude")
    
    print("DEBUG: Starting recorder...")

    recorder.start_recording("hover_simulation")
    print("DEBUG: Recorder started")
    
    # Get initial state
    print("DEBUG: Getting initial state...")
    current_state = dynamics_comp.state
    if current_state is None:
        print("ERROR: Dynamics component state is None!")
        return
    print("DEBUG: Initial state obtained")
    
    try:
        step_count = 0
        current_time = 0.0
        
        print("DEBUG: Entering simulation loop...")
        while current_time < sim_duration:
            if step_count % 100 == 0:
                print(f"DEBUG: Step {step_count}, time={current_time:.3f}s")
            # Attitude control
            control_output = attitude_controller.compute_control(hover_reference, current_state, dt)
            
            # Motor allocation
            motor_speeds = propulsion.allocate_control(control_output)
            motor_speeds_actual = propulsion.update_motors(dt, motor_speeds)
            
            # Dynamics update
            derivatives = dynamics_model.compute_derivatives(current_state, motor_speeds_actual)
            
            # Check for numerical issues
            if np.any(np.isnan(derivatives)) or np.any(np.isinf(derivatives)):
                print(f"❌ Numerical instability at t={current_time:.3f}s")
                break
            
            # Integration with clamping for stability
            new_state_vector = current_state.state_vector + dt * derivatives
            
            # Clamp position (reasonable flight envelope)
            new_state_vector[0:3] = np.clip(new_state_vector[0:3], -100, 100)
            
            # Clamp velocity (reasonable flight speeds)
            new_state_vector[3:6] = np.clip(new_state_vector[3:6], -50, 50)
            
            # Clamp attitude angles to prevent gimbal lock
            new_state_vector[6] = np.clip(new_state_vector[6], -np.pi/2 + 0.1, np.pi/2 - 0.1)  # roll
            new_state_vector[7] = np.clip(new_state_vector[7], -np.pi/2 + 0.1, np.pi/2 - 0.1)  # pitch
            new_state_vector[8] = np.fmod(new_state_vector[8], 2*np.pi)  # yaw (wrap around)
            
            # Clamp angular velocities (reasonable rotation rates)
            new_state_vector[9:12] = np.clip(new_state_vector[9:12], -20, 20)
            
            current_state.state_vector = new_state_vector
            current_state.time = current_time
            
            # Advance time
            current_time += dt
            step_count += 1
            
            # Create results for callbacks
            results = {
                'uav_dynamics': {'state': current_state},
                'attitude_controller': {'control_output': control_output}
            }
            
            # Record data
            recorder.record_step(current_time, results)
            
            # Call callbacks
            for callback in sim.step_callbacks:
                callback(sim, results)
                
            # Update sim time for callbacks
            sim.current_time = current_time
        
        print(f"\nSimulation completed successfully!")
        print(f"Total steps: {step_count}")
        
    except KeyboardInterrupt:
        print("\nSimulation interrupted by user")
    
    finally:
        # Stop recording and save data
        recorder.stop_recording()
        
        # Save data in multiple formats
        print("\nSaving simulation data...")
        hdf5_file = recorder.save_data(format='hdf5')
        json_file = recorder.save_data(format='json')
        
        # Stop visualizations (disabled)
        # state_monitor.stop()
        # control_monitor.stop()
        # trajectory_viewer.stop_animation()
        
        # Print final statistics
        print("\n" + "="*50)
        print("SIMULATION SUMMARY")
        print("="*50)
        
        final_state = current_state
        if final_state:
            print(f"Final Position: [{final_state.position[0]:.3f}, {final_state.position[1]:.3f}, {final_state.position[2]:.3f}] m")
            print(f"Final Attitude: [{np.degrees(final_state.attitude[0]):.1f}, {np.degrees(final_state.attitude[1]):.1f}, {np.degrees(final_state.attitude[2]):.1f}] deg")
            
            # Calculate position error from target
            target_position = np.array([0.0, 0.0, -1.0])
            position_error = np.linalg.norm(final_state.position - target_position)
            print(f"Position Error: {position_error:.3f} m")
            
            # Calculate attitude error from level
            attitude_error = np.linalg.norm(final_state.attitude[:2])  # Ignore yaw
            print(f"Attitude Error: {np.degrees(attitude_error):.1f} deg")
        
        # Perform data analysis (simplified, no plots due to matplotlib issues)
        print("\nPerforming data analysis...")
        try:
            # Load and analyze data
            analyzer = DataAnalyzer()
            data = recorder.load_data(hdf5_file)
            analyzer.load_data(data)
            
            # Generate analysis report (text only)
            report = analyzer.generate_performance_report("hover_simulation_report.txt")
            print("\nPerformance Report:")
            print("-" * 30)
            print(report)
            
            # Skip plots due to matplotlib compatibility issues
            print("Note: Plots skipped due to matplotlib compatibility issues")
            
        except Exception as e:
            print(f"Analysis failed: {e}")
        
        print(f"\nData saved to:")
        print(f"  - HDF5: {hdf5_file}")
        print(f"  - JSON: {json_file}")
        print("\nSimulation complete!")


if __name__ == "__main__":
    main()