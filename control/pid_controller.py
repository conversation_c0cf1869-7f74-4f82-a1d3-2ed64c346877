"""
PID controller implementation
"""

import numpy as np
from typing import Union, Dict, Any
from control.base_controller import BaseController, AttitudeReference
from core.state import UAVState


class PIDController(BaseController):
    """
    Generic PID controller with anti-windup and derivative filtering
    """
    
    def __init__(self, name: str, kp: float = 1.0, ki: float = 0.0, kd: float = 0.0,
                 output_limits: tuple = None, derivative_filter: float = 0.0,
                 anti_windup: bool = True):
        """
        Initialize PID controller
        
        Args:
            name: Controller name
            kp: Proportional gain
            ki: Integral gain  
            kd: Derivative gain
            output_limits: (min, max) output saturation limits
            derivative_filter: Low-pass filter coefficient for derivative term
            anti_windup: Enable integral anti-windup
        """
        super().__init__(name)
        
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.output_limits = output_limits
        self.derivative_filter = derivative_filter
        self.anti_windup = anti_windup
        
        # Internal states
        self.error = 0.0
        self.last_error = 0.0
        self.integral_error = 0.0
        self.derivative_error = 0.0
        self.filtered_derivative = 0.0
        
        # Store parameters
        self.parameters = {
            'kp': kp, 'ki': ki, 'kd': kd,
            'output_limits': output_limits,
            'derivative_filter': derivative_filter,
            'anti_windup': anti_windup
        }
    
    def compute_control(self, reference: float, current_value: float, dt: float) -> float:
        """
        Compute PID control output
        
        Args:
            reference: Reference/setpoint value
            current_value: Current measured value
            dt: Time step (s)
            
        Returns:
            Control output
        """
        if not self.enabled:
            return 0.0
        
        # Compute error
        self.error = reference - current_value
        
        # Derivative term (compute first for anti-windup)
        raw_derivative = (self.error - self.last_error) / dt if dt > 0 else 0.0
        
        if self.derivative_filter > 0:
            # Low-pass filter: y[n] = α*x[n] + (1-α)*y[n-1]
            alpha = dt / (self.derivative_filter + dt)
            self.filtered_derivative = alpha * raw_derivative + (1 - alpha) * self.filtered_derivative
            self.derivative_error = self.filtered_derivative
        else:
            self.derivative_error = raw_derivative
        
        # Proportional term
        p_term = self.kp * self.error
        
        # Derivative term
        d_term = self.kd * self.derivative_error
        
        # Integral term with anti-windup
        if self.anti_windup and self.output_limits:
            # Only integrate if output is not saturated or error would reduce saturation
            prev_output = p_term + self.ki * self.integral_error + d_term
            if (self.output_limits[0] < prev_output < self.output_limits[1] or
                (prev_output <= self.output_limits[0] and self.error > 0) or
                (prev_output >= self.output_limits[1] and self.error < 0)):
                self.integral_error += self.error * dt
        else:
            self.integral_error += self.error * dt
        
        i_term = self.ki * self.integral_error
        
        # Compute total output
        output = p_term + i_term + d_term
        
        # Apply output limits
        if self.output_limits:
            output = np.clip(output, self.output_limits[0], self.output_limits[1])
        
        # Store for next iteration
        self.last_error = self.error
        
        return output
    
    def reset(self):
        """Reset controller internal states"""
        self.error = 0.0
        self.last_error = 0.0
        self.integral_error = 0.0
        self.derivative_error = 0.0
        self.filtered_derivative = 0.0
    
    def set_gains(self, kp: float = None, ki: float = None, kd: float = None):
        """Update PID gains"""
        if kp is not None:
            self.kp = kp
            self.parameters['kp'] = kp
        if ki is not None:
            self.ki = ki
            self.parameters['ki'] = ki
        if kd is not None:
            self.kd = kd
            self.parameters['kd'] = kd


class VectorPIDController(BaseController):
    """
    Vector PID controller for multi-dimensional control
    """
    
    def __init__(self, name: str, dimension: int, 
                 kp: Union[float, np.ndarray] = 1.0,
                 ki: Union[float, np.ndarray] = 0.0,
                 kd: Union[float, np.ndarray] = 0.0,
                 output_limits: tuple = None):
        """
        Initialize vector PID controller
        
        Args:
            name: Controller name
            dimension: Control vector dimension
            kp, ki, kd: Gains (scalar or vector)
            output_limits: (min, max) output limits per channel
        """
        super().__init__(name)
        
        self.dimension = dimension
        
        # Convert gains to vectors
        self.kp = np.full(dimension, kp) if np.isscalar(kp) else np.array(kp)
        self.ki = np.full(dimension, ki) if np.isscalar(ki) else np.array(ki)
        self.kd = np.full(dimension, kd) if np.isscalar(kd) else np.array(kd)
        
        self.output_limits = output_limits
        
        # Internal states
        self.error = np.zeros(dimension)
        self.last_error = np.zeros(dimension)
        self.integral_error = np.zeros(dimension)
        self.derivative_error = np.zeros(dimension)
    
    def compute_control(self, reference: np.ndarray, current_value: np.ndarray, 
                       dt: float) -> np.ndarray:
        """
        Compute vector PID control output
        
        Args:
            reference: Reference vector
            current_value: Current measured vector
            dt: Time step (s)
            
        Returns:
            Control output vector
        """
        if not self.enabled:
            return np.zeros(self.dimension)
        
        # Compute error
        self.error = np.array(reference) - np.array(current_value)
        
        # PID terms
        p_term = self.kp * self.error
        self.integral_error += self.error * dt
        i_term = self.ki * self.integral_error
        
        if dt > 0:
            self.derivative_error = (self.error - self.last_error) / dt
        else:
            self.derivative_error = np.zeros(self.dimension)
        
        d_term = self.kd * self.derivative_error
        
        # Total output
        output = p_term + i_term + d_term
        
        # Apply limits
        if self.output_limits:
            output = np.clip(output, self.output_limits[0], self.output_limits[1])
        
        self.last_error = self.error.copy()
        
        return output
    
    def reset(self):
        """Reset controller states"""
        self.error = np.zeros(self.dimension)
        self.last_error = np.zeros(self.dimension)
        self.integral_error = np.zeros(self.dimension)
        self.derivative_error = np.zeros(self.dimension)


class AttitudePIDController(BaseController):
    """
    Attitude PID controller for quadrotor
    
    Controls roll, pitch, yaw angles and thrust
    """
    
    def __init__(self, name: str = "AttitudePID", mass: float = 1.5, gravity: float = 9.81):
        """
        Initialize attitude controller
        
        Args:
            name: Controller name
            mass: Vehicle mass (kg)
            gravity: Gravitational acceleration (m/s^2)
        """
        super().__init__(name)
        
        self.mass = mass
        self.gravity = gravity
        self.hover_thrust = mass * gravity
        
        # Individual PID controllers for each axis  
        self.roll_pid = PIDController("roll", kp=4.0, ki=0.1, kd=0.2, 
                                     output_limits=(-5.0, 5.0))
        self.pitch_pid = PIDController("pitch", kp=4.0, ki=0.1, kd=0.2,
                                      output_limits=(-5.0, 5.0))
        self.yaw_pid = PIDController("yaw", kp=2.0, ki=0.05, kd=0.1,
                                    output_limits=(-2.0, 2.0))
        
        # Thrust controller (simple proportional for altitude)
        self.thrust_gain = 5.0
        
        # Store parameters
        self.parameters = {
            'mass': mass,
            'gravity': gravity,
            'roll_gains': self.roll_pid.get_parameters(),
            'pitch_gains': self.pitch_pid.get_parameters(),
            'yaw_gains': self.yaw_pid.get_parameters(),
            'thrust_gain': self.thrust_gain
        }
    
    def compute_control(self, reference: AttitudeReference, 
                       current_state: UAVState, dt: float) -> np.ndarray:
        """
        Compute attitude control outputs
        
        Args:
            reference: Attitude reference (roll, pitch, yaw, thrust)
            current_state: Current UAV state
            dt: Time step (s)
            
        Returns:
            Control moments [Mx, My, Mz, T] (N*m, N)
        """
        if not self.enabled:
            return np.array([0.0, 0.0, 0.0, self.hover_thrust])
        
        # Current attitude
        current_attitude = current_state.attitude
        
        # Compute attitude errors and control moments
        roll_moment = self.roll_pid.compute_control(
            reference.roll, current_attitude[0], dt
        )
        
        pitch_moment = self.pitch_pid.compute_control(
            reference.pitch, current_attitude[1], dt
        )
        
        yaw_moment = self.yaw_pid.compute_control(
            reference.yaw, current_attitude[2], dt
        )
        
        # Thrust control
        if reference.thrust is not None:
            thrust = reference.thrust
        else:
            # Default hover thrust
            thrust = self.hover_thrust
        
        return np.array([roll_moment, pitch_moment, yaw_moment, thrust])
    
    def reset(self):
        """Reset all PID controllers"""
        self.roll_pid.reset()
        self.pitch_pid.reset()
        self.yaw_pid.reset()
    
    def set_attitude_gains(self, axis: str, kp: float = None, 
                          ki: float = None, kd: float = None):
        """
        Set gains for specific attitude axis
        
        Args:
            axis: 'roll', 'pitch', or 'yaw'
            kp, ki, kd: PID gains
        """
        if axis == 'roll':
            self.roll_pid.set_gains(kp, ki, kd)
        elif axis == 'pitch':
            self.pitch_pid.set_gains(kp, ki, kd)
        elif axis == 'yaw':
            self.yaw_pid.set_gains(kp, ki, kd)
        else:
            raise ValueError(f"Unknown axis: {axis}")
    
    def get_attitude_errors(self) -> Dict[str, float]:
        """Get current attitude errors"""
        return {
            'roll_error': self.roll_pid.error,
            'pitch_error': self.pitch_pid.error,
            'yaw_error': self.yaw_pid.error
        }