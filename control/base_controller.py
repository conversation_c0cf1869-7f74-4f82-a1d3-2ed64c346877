"""
Base controller class and interfaces
"""

from abc import ABC, abstractmethod
import numpy as np
from typing import Dict, Any, Optional, Union
from core.state import UAVState


class BaseController(ABC):
    """
    Abstract base class for all controllers
    
    Provides standardized interface for different control algorithms
    """
    
    def __init__(self, name: str):
        """
        Initialize controller
        
        Args:
            name: Controller name/identifier
        """
        self.name = name
        self.parameters = {}
        self.enabled = True
        self.error = None
        self.last_error = None
        self.integral_error = None
        self.derivative_error = None
    
    @abstractmethod
    def compute_control(self, reference: Any, current_state: Any, dt: float) -> np.ndarray:
        """
        Compute control output
        
        Args:
            reference: Reference/setpoint signal
            current_state: Current system state
            dt: Time step (s)
            
        Returns:
            Control output signal
        """
        pass
    
    @abstractmethod
    def reset(self):
        """Reset controller internal states"""
        pass
    
    def set_parameters(self, **kwargs):
        """Set controller parameters"""
        self.parameters.update(kwargs)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get controller parameters"""
        return self.parameters.copy()
    
    def enable(self):
        """Enable controller"""
        self.enabled = True
    
    def disable(self):
        """Disable controller"""
        self.enabled = False
    
    def get_error_info(self) -> Dict[str, Any]:
        """Get error information for tuning/debugging"""
        return {
            'current_error': self.error,
            'last_error': self.last_error,
            'integral_error': self.integral_error,
            'derivative_error': self.derivative_error
        }


class AttitudeReference:
    """Reference signal for attitude control"""
    
    def __init__(self, roll: float = 0.0, pitch: float = 0.0, 
                 yaw: float = 0.0, thrust: float = None):
        """
        Initialize attitude reference
        
        Args:
            roll: Roll angle reference (rad)
            pitch: Pitch angle reference (rad)
            yaw: Yaw angle reference (rad)
            thrust: Thrust reference (N), if None use hover thrust
        """
        self.roll = roll
        self.pitch = pitch
        self.yaw = yaw
        self.thrust = thrust
        
        self.attitude = np.array([roll, pitch, yaw])
    
    def to_array(self) -> np.ndarray:
        """Convert to numpy array"""
        return self.attitude.copy()
    
    def update(self, roll: float = None, pitch: float = None, 
               yaw: float = None, thrust: float = None):
        """Update reference values"""
        if roll is not None:
            self.roll = roll
            self.attitude[0] = roll
        if pitch is not None:
            self.pitch = pitch
            self.attitude[1] = pitch
        if yaw is not None:
            self.yaw = yaw
            self.attitude[2] = yaw
        if thrust is not None:
            self.thrust = thrust


class PositionReference:
    """Reference signal for position control"""
    
    def __init__(self, x: float = 0.0, y: float = 0.0, z: float = 0.0,
                 vx: float = 0.0, vy: float = 0.0, vz: float = 0.0,
                 yaw: float = 0.0):
        """
        Initialize position reference
        
        Args:
            x, y, z: Position references (m)
            vx, vy, vz: Velocity references (m/s)
            yaw: Yaw angle reference (rad)
        """
        self.position = np.array([x, y, z])
        self.velocity = np.array([vx, vy, vz])
        self.yaw = yaw
    
    def update_position(self, x: float = None, y: float = None, z: float = None):
        """Update position reference"""
        if x is not None:
            self.position[0] = x
        if y is not None:
            self.position[1] = y
        if z is not None:
            self.position[2] = z
    
    def update_velocity(self, vx: float = None, vy: float = None, vz: float = None):
        """Update velocity reference"""
        if vx is not None:
            self.velocity[0] = vx
        if vy is not None:
            self.velocity[1] = vy
        if vz is not None:
            self.velocity[2] = vz
    
    def update_yaw(self, yaw: float):
        """Update yaw reference"""
        self.yaw = yaw


class TrajectoryReference:
    """Time-based trajectory reference"""
    
    def __init__(self):
        """Initialize empty trajectory"""
        self.time_points = []
        self.position_points = []
        self.velocity_points = []
        self.yaw_points = []
        self.current_index = 0
    
    def add_waypoint(self, time: float, position: np.ndarray, 
                    velocity: np.ndarray = None, yaw: float = 0.0):
        """
        Add waypoint to trajectory
        
        Args:
            time: Time point (s)
            position: Position [x, y, z] (m)
            velocity: Velocity [vx, vy, vz] (m/s)
            yaw: Yaw angle (rad)
        """
        self.time_points.append(time)
        self.position_points.append(np.array(position))
        self.velocity_points.append(
            np.array(velocity) if velocity is not None else np.zeros(3)
        )
        self.yaw_points.append(yaw)
    
    def get_reference(self, current_time: float) -> PositionReference:
        """
        Get reference at current time using interpolation
        
        Args:
            current_time: Current simulation time (s)
            
        Returns:
            Position reference at current time
        """
        if not self.time_points:
            return PositionReference()
        
        # Find interpolation indices
        times = np.array(self.time_points)
        
        if current_time <= times[0]:
            # Before first point
            pos = self.position_points[0]
            vel = self.velocity_points[0]
            yaw = self.yaw_points[0]
        elif current_time >= times[-1]:
            # After last point
            pos = self.position_points[-1]
            vel = self.velocity_points[-1]
            yaw = self.yaw_points[-1]
        else:
            # Interpolate between points
            idx = np.searchsorted(times, current_time) - 1
            t1, t2 = times[idx], times[idx + 1]
            alpha = (current_time - t1) / (t2 - t1)
            
            pos = (1 - alpha) * self.position_points[idx] + alpha * self.position_points[idx + 1]
            vel = (1 - alpha) * self.velocity_points[idx] + alpha * self.velocity_points[idx + 1]
            yaw = (1 - alpha) * self.yaw_points[idx] + alpha * self.yaw_points[idx + 1]
        
        return PositionReference(
            pos[0], pos[1], pos[2],
            vel[0], vel[1], vel[2],
            yaw
        )
    
    def clear(self):
        """Clear all waypoints"""
        self.time_points.clear()
        self.position_points.clear()
        self.velocity_points.clear()
        self.yaw_points.clear()
        self.current_index = 0


class ControllerModes:
    """Control mode definitions"""
    STABILIZE = "stabilize"  # Stabilize mode (manual attitude control)
    ALTITUDE_HOLD = "altitude_hold"  # Altitude hold mode
    POSITION_HOLD = "position_hold"  # Position hold mode
    GUIDED = "guided"  # Guided mode (trajectory following)
    AUTO = "auto"  # Autonomous mission mode
    MANUAL = "manual"  # Full manual control


class MultiModeController(BaseController):
    """
    Multi-mode controller that switches between different control strategies
    """
    
    def __init__(self, name: str = "MultiModeController"):
        super().__init__(name)
        self.mode = ControllerModes.STABILIZE
        self.controllers = {}  # mode -> controller mapping
        self.mode_transitions = {}  # Allowed mode transitions
    
    def add_controller(self, mode: str, controller: BaseController):
        """Add controller for specific mode"""
        self.controllers[mode] = controller
    
    def set_mode(self, mode: str) -> bool:
        """
        Switch to new control mode
        
        Args:
            mode: Target control mode
            
        Returns:
            True if mode switch successful
        """
        if mode not in self.controllers:
            print(f"Warning: Mode '{mode}' not available")
            return False
        
        # Check if transition is allowed (if transitions are defined)
        if (self.mode_transitions and 
            self.mode in self.mode_transitions and 
            mode not in self.mode_transitions[self.mode]):
            print(f"Warning: Transition from '{self.mode}' to '{mode}' not allowed")
            return False
        
        print(f"Switching from '{self.mode}' to '{mode}' mode")
        self.mode = mode
        return True
    
    def compute_control(self, reference: Any, current_state: Any, dt: float) -> np.ndarray:
        """Compute control using current mode controller"""
        if not self.enabled:
            return np.zeros(4)
        
        if self.mode not in self.controllers:
            print(f"Error: No controller for mode '{self.mode}'")
            return np.zeros(4)
        
        controller = self.controllers[self.mode]
        return controller.compute_control(reference, current_state, dt)
    
    def reset(self):
        """Reset all controllers"""
        for controller in self.controllers.values():
            controller.reset()
    
    def get_current_controller(self) -> Optional[BaseController]:
        """Get currently active controller"""
        return self.controllers.get(self.mode)